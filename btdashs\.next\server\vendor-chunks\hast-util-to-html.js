"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/hast-util-to-html";
exports.ids = ["vendor-chunks/hast-util-to-html"];
exports.modules = {

/***/ "(ssr)/./node_modules/hast-util-to-html/lib/handle/comment.js":
/*!**************************************************************!*\
  !*** ./node_modules/hast-util-to-html/lib/handle/comment.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   comment: () => (/* binding */ comment)\n/* harmony export */ });\n/* harmony import */ var stringify_entities__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! stringify-entities */ \"(ssr)/./node_modules/stringify-entities/lib/index.js\");\n/**\n * @import {Comment, Parents} from 'hast'\n * @import {State} from '../index.js'\n */\n\n\n\nconst htmlCommentRegex = /^>|^->|<!--|-->|--!>|<!-$/g\n\n// Declare arrays as variables so it can be cached by `stringifyEntities`\nconst bogusCommentEntitySubset = ['>']\nconst commentEntitySubset = ['<', '>']\n\n/**\n * Serialize a comment.\n *\n * @param {Comment} node\n *   Node to handle.\n * @param {number | undefined} _1\n *   Index of `node` in `parent.\n * @param {Parents | undefined} _2\n *   Parent of `node`.\n * @param {State} state\n *   Info passed around about the current state.\n * @returns {string}\n *   Serialized node.\n */\nfunction comment(node, _1, _2, state) {\n  // See: <https://html.spec.whatwg.org/multipage/syntax.html#comments>\n  return state.settings.bogusComments\n    ? '<?' +\n        (0,stringify_entities__WEBPACK_IMPORTED_MODULE_0__.stringifyEntities)(\n          node.value,\n          Object.assign({}, state.settings.characterReferences, {\n            subset: bogusCommentEntitySubset\n          })\n        ) +\n        '>'\n    : '<!--' + node.value.replace(htmlCommentRegex, encode) + '-->'\n\n  /**\n   * @param {string} $0\n   */\n  function encode($0) {\n    return (0,stringify_entities__WEBPACK_IMPORTED_MODULE_0__.stringifyEntities)(\n      $0,\n      Object.assign({}, state.settings.characterReferences, {\n        subset: commentEntitySubset\n      })\n    )\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-html/lib/handle/comment.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-html/lib/handle/doctype.js":
/*!**************************************************************!*\
  !*** ./node_modules/hast-util-to-html/lib/handle/doctype.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   doctype: () => (/* binding */ doctype)\n/* harmony export */ });\n/**\n * @import {Doctype, Parents} from 'hast'\n * @import {State} from '../index.js'\n */\n\n/**\n * Serialize a doctype.\n *\n * @param {Doctype} _1\n *   Node to handle.\n * @param {number | undefined} _2\n *   Index of `node` in `parent.\n * @param {Parents | undefined} _3\n *   Parent of `node`.\n * @param {State} state\n *   Info passed around about the current state.\n * @returns {string}\n *   Serialized node.\n */\nfunction doctype(_1, _2, _3, state) {\n  return (\n    '<!' +\n    (state.settings.upperDoctype ? 'DOCTYPE' : 'doctype') +\n    (state.settings.tightDoctype ? '' : ' ') +\n    'html>'\n  )\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLWh0bWwvbGliL2hhbmRsZS9kb2N0eXBlLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBLFlBQVksa0JBQWtCO0FBQzlCLFlBQVksT0FBTztBQUNuQjs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLFNBQVM7QUFDcEI7QUFDQSxXQUFXLG9CQUFvQjtBQUMvQjtBQUNBLFdBQVcscUJBQXFCO0FBQ2hDO0FBQ0EsV0FBVyxPQUFPO0FBQ2xCO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGJlbmppXFxEZXNrdG9wXFxHX1BST0dcXE5pY29sYXNcXGJ0ZGFzaC1lY29zeXN0ZW1cXGJ0ZGFzaHNcXG5vZGVfbW9kdWxlc1xcaGFzdC11dGlsLXRvLWh0bWxcXGxpYlxcaGFuZGxlXFxkb2N0eXBlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGltcG9ydCB7RG9jdHlwZSwgUGFyZW50c30gZnJvbSAnaGFzdCdcbiAqIEBpbXBvcnQge1N0YXRlfSBmcm9tICcuLi9pbmRleC5qcydcbiAqL1xuXG4vKipcbiAqIFNlcmlhbGl6ZSBhIGRvY3R5cGUuXG4gKlxuICogQHBhcmFtIHtEb2N0eXBlfSBfMVxuICogICBOb2RlIHRvIGhhbmRsZS5cbiAqIEBwYXJhbSB7bnVtYmVyIHwgdW5kZWZpbmVkfSBfMlxuICogICBJbmRleCBvZiBgbm9kZWAgaW4gYHBhcmVudC5cbiAqIEBwYXJhbSB7UGFyZW50cyB8IHVuZGVmaW5lZH0gXzNcbiAqICAgUGFyZW50IG9mIGBub2RlYC5cbiAqIEBwYXJhbSB7U3RhdGV9IHN0YXRlXG4gKiAgIEluZm8gcGFzc2VkIGFyb3VuZCBhYm91dCB0aGUgY3VycmVudCBzdGF0ZS5cbiAqIEByZXR1cm5zIHtzdHJpbmd9XG4gKiAgIFNlcmlhbGl6ZWQgbm9kZS5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGRvY3R5cGUoXzEsIF8yLCBfMywgc3RhdGUpIHtcbiAgcmV0dXJuIChcbiAgICAnPCEnICtcbiAgICAoc3RhdGUuc2V0dGluZ3MudXBwZXJEb2N0eXBlID8gJ0RPQ1RZUEUnIDogJ2RvY3R5cGUnKSArXG4gICAgKHN0YXRlLnNldHRpbmdzLnRpZ2h0RG9jdHlwZSA/ICcnIDogJyAnKSArXG4gICAgJ2h0bWw+J1xuICApXG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-html/lib/handle/doctype.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-html/lib/handle/element.js":
/*!**************************************************************!*\
  !*** ./node_modules/hast-util-to-html/lib/handle/element.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   element: () => (/* binding */ element)\n/* harmony export */ });\n/* harmony import */ var ccount__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ccount */ \"(ssr)/./node_modules/ccount/index.js\");\n/* harmony import */ var comma_separated_tokens__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! comma-separated-tokens */ \"(ssr)/./node_modules/comma-separated-tokens/index.js\");\n/* harmony import */ var property_information__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! property-information */ \"(ssr)/./node_modules/property-information/index.js\");\n/* harmony import */ var property_information__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! property-information */ \"(ssr)/./node_modules/property-information/lib/find.js\");\n/* harmony import */ var space_separated_tokens__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! space-separated-tokens */ \"(ssr)/./node_modules/space-separated-tokens/index.js\");\n/* harmony import */ var stringify_entities__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! stringify-entities */ \"(ssr)/./node_modules/stringify-entities/lib/index.js\");\n/* harmony import */ var _omission_closing_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../omission/closing.js */ \"(ssr)/./node_modules/hast-util-to-html/lib/omission/closing.js\");\n/* harmony import */ var _omission_opening_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../omission/opening.js */ \"(ssr)/./node_modules/hast-util-to-html/lib/omission/opening.js\");\n/**\n * @import {Element, Parents, Properties} from 'hast'\n * @import {State} from '../index.js'\n */\n\n\n\n\n\n\n\n\n\n/**\n * Maps of subsets.\n *\n * Each value is a matrix of tuples.\n * The value at `0` causes parse errors, the value at `1` is valid.\n * Of both, the value at `0` is unsafe, and the value at `1` is safe.\n *\n * @type {Record<'double' | 'name' | 'single' | 'unquoted', Array<[Array<string>, Array<string>]>>}\n */\nconst constants = {\n  // See: <https://html.spec.whatwg.org/#attribute-name-state>.\n  name: [\n    ['\\t\\n\\f\\r &/=>'.split(''), '\\t\\n\\f\\r \"&\\'/=>`'.split('')],\n    ['\\0\\t\\n\\f\\r \"&\\'/<=>'.split(''), '\\0\\t\\n\\f\\r \"&\\'/<=>`'.split('')]\n  ],\n  // See: <https://html.spec.whatwg.org/#attribute-value-(unquoted)-state>.\n  unquoted: [\n    ['\\t\\n\\f\\r &>'.split(''), '\\0\\t\\n\\f\\r \"&\\'<=>`'.split('')],\n    ['\\0\\t\\n\\f\\r \"&\\'<=>`'.split(''), '\\0\\t\\n\\f\\r \"&\\'<=>`'.split('')]\n  ],\n  // See: <https://html.spec.whatwg.org/#attribute-value-(single-quoted)-state>.\n  single: [\n    [\"&'\".split(''), '\"&\\'`'.split('')],\n    [\"\\0&'\".split(''), '\\0\"&\\'`'.split('')]\n  ],\n  // See: <https://html.spec.whatwg.org/#attribute-value-(double-quoted)-state>.\n  double: [\n    ['\"&'.split(''), '\"&\\'`'.split('')],\n    ['\\0\"&'.split(''), '\\0\"&\\'`'.split('')]\n  ]\n}\n\n/**\n * Serialize an element node.\n *\n * @param {Element} node\n *   Node to handle.\n * @param {number | undefined} index\n *   Index of `node` in `parent.\n * @param {Parents | undefined} parent\n *   Parent of `node`.\n * @param {State} state\n *   Info passed around about the current state.\n * @returns {string}\n *   Serialized node.\n */\nfunction element(node, index, parent, state) {\n  const schema = state.schema\n  const omit = schema.space === 'svg' ? false : state.settings.omitOptionalTags\n  let selfClosing =\n    schema.space === 'svg'\n      ? state.settings.closeEmptyElements\n      : state.settings.voids.includes(node.tagName.toLowerCase())\n  /** @type {Array<string>} */\n  const parts = []\n  /** @type {string} */\n  let last\n\n  if (schema.space === 'html' && node.tagName === 'svg') {\n    state.schema = property_information__WEBPACK_IMPORTED_MODULE_0__.svg\n  }\n\n  const attributes = serializeAttributes(state, node.properties)\n\n  const content = state.all(\n    schema.space === 'html' && node.tagName === 'template' ? node.content : node\n  )\n\n  state.schema = schema\n\n  // If the node is categorised as void, but it has children, remove the\n  // categorisation.\n  // This enables for example `menuitem`s, which are void in W3C HTML but not\n  // void in WHATWG HTML, to be stringified properly.\n  // Note: `menuitem` has since been removed from the HTML spec, and so is no\n  // longer void.\n  if (content) selfClosing = false\n\n  if (attributes || !omit || !(0,_omission_opening_js__WEBPACK_IMPORTED_MODULE_1__.opening)(node, index, parent)) {\n    parts.push('<', node.tagName, attributes ? ' ' + attributes : '')\n\n    if (\n      selfClosing &&\n      (schema.space === 'svg' || state.settings.closeSelfClosing)\n    ) {\n      last = attributes.charAt(attributes.length - 1)\n      if (\n        !state.settings.tightSelfClosing ||\n        last === '/' ||\n        (last && last !== '\"' && last !== \"'\")\n      ) {\n        parts.push(' ')\n      }\n\n      parts.push('/')\n    }\n\n    parts.push('>')\n  }\n\n  parts.push(content)\n\n  if (!selfClosing && (!omit || !(0,_omission_closing_js__WEBPACK_IMPORTED_MODULE_2__.closing)(node, index, parent))) {\n    parts.push('</' + node.tagName + '>')\n  }\n\n  return parts.join('')\n}\n\n/**\n * @param {State} state\n * @param {Properties | null | undefined} properties\n * @returns {string}\n */\nfunction serializeAttributes(state, properties) {\n  /** @type {Array<string>} */\n  const values = []\n  let index = -1\n  /** @type {string} */\n  let key\n\n  if (properties) {\n    for (key in properties) {\n      if (properties[key] !== null && properties[key] !== undefined) {\n        const value = serializeAttribute(state, key, properties[key])\n        if (value) values.push(value)\n      }\n    }\n  }\n\n  while (++index < values.length) {\n    const last = state.settings.tightAttributes\n      ? values[index].charAt(values[index].length - 1)\n      : undefined\n\n    // In tight mode, don’t add a space after quoted attributes.\n    if (index !== values.length - 1 && last !== '\"' && last !== \"'\") {\n      values[index] += ' '\n    }\n  }\n\n  return values.join('')\n}\n\n/**\n * @param {State} state\n * @param {string} key\n * @param {Properties[keyof Properties]} value\n * @returns {string}\n */\nfunction serializeAttribute(state, key, value) {\n  const info = (0,property_information__WEBPACK_IMPORTED_MODULE_3__.find)(state.schema, key)\n  const x =\n    state.settings.allowParseErrors && state.schema.space === 'html' ? 0 : 1\n  const y = state.settings.allowDangerousCharacters ? 0 : 1\n  let quote = state.quote\n  /** @type {string | undefined} */\n  let result\n\n  if (info.overloadedBoolean && (value === info.attribute || value === '')) {\n    value = true\n  } else if (\n    (info.boolean || info.overloadedBoolean) &&\n    (typeof value !== 'string' || value === info.attribute || value === '')\n  ) {\n    value = Boolean(value)\n  }\n\n  if (\n    value === null ||\n    value === undefined ||\n    value === false ||\n    (typeof value === 'number' && Number.isNaN(value))\n  ) {\n    return ''\n  }\n\n  const name = (0,stringify_entities__WEBPACK_IMPORTED_MODULE_4__.stringifyEntities)(\n    info.attribute,\n    Object.assign({}, state.settings.characterReferences, {\n      // Always encode without parse errors in non-HTML.\n      subset: constants.name[x][y]\n    })\n  )\n\n  // No value.\n  // There is currently only one boolean property in SVG: `[download]` on\n  // `<a>`.\n  // This property does not seem to work in browsers (Firefox, Safari, Chrome),\n  // so I can’t test if dropping the value works.\n  // But I assume that it should:\n  //\n  // ```html\n  // <!doctype html>\n  // <svg viewBox=\"0 0 100 100\">\n  //   <a href=https://example.com download>\n  //     <circle cx=50 cy=40 r=35 />\n  //   </a>\n  // </svg>\n  // ```\n  //\n  // See: <https://github.com/wooorm/property-information/blob/main/lib/svg.js>\n  if (value === true) return name\n\n  // `spaces` doesn’t accept a second argument, but it’s given here just to\n  // keep the code cleaner.\n  value = Array.isArray(value)\n    ? (info.commaSeparated ? comma_separated_tokens__WEBPACK_IMPORTED_MODULE_5__.stringify : space_separated_tokens__WEBPACK_IMPORTED_MODULE_6__.stringify)(value, {\n        padLeft: !state.settings.tightCommaSeparatedLists\n      })\n    : String(value)\n\n  if (state.settings.collapseEmptyAttributes && !value) return name\n\n  // Check unquoted value.\n  if (state.settings.preferUnquoted) {\n    result = (0,stringify_entities__WEBPACK_IMPORTED_MODULE_4__.stringifyEntities)(\n      value,\n      Object.assign({}, state.settings.characterReferences, {\n        attribute: true,\n        subset: constants.unquoted[x][y]\n      })\n    )\n  }\n\n  // If we don’t want unquoted, or if `value` contains character references when\n  // unquoted…\n  if (result !== value) {\n    // If the alternative is less common than `quote`, switch.\n    if (\n      state.settings.quoteSmart &&\n      (0,ccount__WEBPACK_IMPORTED_MODULE_7__.ccount)(value, quote) > (0,ccount__WEBPACK_IMPORTED_MODULE_7__.ccount)(value, state.alternative)\n    ) {\n      quote = state.alternative\n    }\n\n    result =\n      quote +\n      (0,stringify_entities__WEBPACK_IMPORTED_MODULE_4__.stringifyEntities)(\n        value,\n        Object.assign({}, state.settings.characterReferences, {\n          // Always encode without parse errors in non-HTML.\n          subset: (quote === \"'\" ? constants.single : constants.double)[x][y],\n          attribute: true\n        })\n      ) +\n      quote\n  }\n\n  // Don’t add a `=` for unquoted empties.\n  return name + (result ? '=' + result : result)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-html/lib/handle/element.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-html/lib/handle/index.js":
/*!************************************************************!*\
  !*** ./node_modules/hast-util-to-html/lib/handle/index.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   handle: () => (/* binding */ handle)\n/* harmony export */ });\n/* harmony import */ var zwitch__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zwitch */ \"(ssr)/./node_modules/zwitch/index.js\");\n/* harmony import */ var _comment_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./comment.js */ \"(ssr)/./node_modules/hast-util-to-html/lib/handle/comment.js\");\n/* harmony import */ var _doctype_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./doctype.js */ \"(ssr)/./node_modules/hast-util-to-html/lib/handle/doctype.js\");\n/* harmony import */ var _element_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./element.js */ \"(ssr)/./node_modules/hast-util-to-html/lib/handle/element.js\");\n/* harmony import */ var _raw_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./raw.js */ \"(ssr)/./node_modules/hast-util-to-html/lib/handle/raw.js\");\n/* harmony import */ var _root_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./root.js */ \"(ssr)/./node_modules/hast-util-to-html/lib/handle/root.js\");\n/* harmony import */ var _text_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./text.js */ \"(ssr)/./node_modules/hast-util-to-html/lib/handle/text.js\");\n/**\n * @import {Nodes, Parents} from 'hast'\n * @import {State} from '../index.js'\n */\n\n\n\n\n\n\n\n\n\n/**\n * @type {(node: Nodes, index: number | undefined, parent: Parents | undefined, state: State) => string}\n */\nconst handle = (0,zwitch__WEBPACK_IMPORTED_MODULE_0__.zwitch)('type', {\n  invalid,\n  unknown,\n  handlers: {comment: _comment_js__WEBPACK_IMPORTED_MODULE_1__.comment, doctype: _doctype_js__WEBPACK_IMPORTED_MODULE_2__.doctype, element: _element_js__WEBPACK_IMPORTED_MODULE_3__.element, raw: _raw_js__WEBPACK_IMPORTED_MODULE_4__.raw, root: _root_js__WEBPACK_IMPORTED_MODULE_5__.root, text: _text_js__WEBPACK_IMPORTED_MODULE_6__.text}\n})\n\n/**\n * Fail when a non-node is found in the tree.\n *\n * @param {unknown} node\n *   Unknown value.\n * @returns {never}\n *   Never.\n */\nfunction invalid(node) {\n  throw new Error('Expected node, not `' + node + '`')\n}\n\n/**\n * Fail when a node with an unknown type is found in the tree.\n *\n * @param {unknown} node_\n *  Unknown node.\n * @returns {never}\n *   Never.\n */\nfunction unknown(node_) {\n  // `type` is guaranteed by runtime JS.\n  const node = /** @type {Nodes} */ (node_)\n  throw new Error('Cannot compile unknown node `' + node.type + '`')\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-html/lib/handle/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-html/lib/handle/raw.js":
/*!**********************************************************!*\
  !*** ./node_modules/hast-util-to-html/lib/handle/raw.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   raw: () => (/* binding */ raw)\n/* harmony export */ });\n/* harmony import */ var _text_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./text.js */ \"(ssr)/./node_modules/hast-util-to-html/lib/handle/text.js\");\n/**\n * @import {Parents} from 'hast'\n * @import {Raw} from 'mdast-util-to-hast'\n * @import {State} from '../index.js'\n */\n\n\n\n/**\n * Serialize a raw node.\n *\n * @param {Raw} node\n *   Node to handle.\n * @param {number | undefined} index\n *   Index of `node` in `parent.\n * @param {Parents | undefined} parent\n *   Parent of `node`.\n * @param {State} state\n *   Info passed around about the current state.\n * @returns {string}\n *   Serialized node.\n */\nfunction raw(node, index, parent, state) {\n  return state.settings.allowDangerousHtml\n    ? node.value\n    : (0,_text_js__WEBPACK_IMPORTED_MODULE_0__.text)(node, index, parent, state)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLWh0bWwvbGliL2hhbmRsZS9yYXcuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNBLFlBQVksU0FBUztBQUNyQixZQUFZLEtBQUs7QUFDakIsWUFBWSxPQUFPO0FBQ25COztBQUU4Qjs7QUFFOUI7QUFDQTtBQUNBO0FBQ0EsV0FBVyxLQUFLO0FBQ2hCO0FBQ0EsV0FBVyxvQkFBb0I7QUFDL0I7QUFDQSxXQUFXLHFCQUFxQjtBQUNoQztBQUNBLFdBQVcsT0FBTztBQUNsQjtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0EsTUFBTSw4Q0FBSTtBQUNWIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGJlbmppXFxEZXNrdG9wXFxHX1BST0dcXE5pY29sYXNcXGJ0ZGFzaC1lY29zeXN0ZW1cXGJ0ZGFzaHNcXG5vZGVfbW9kdWxlc1xcaGFzdC11dGlsLXRvLWh0bWxcXGxpYlxcaGFuZGxlXFxyYXcuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAaW1wb3J0IHtQYXJlbnRzfSBmcm9tICdoYXN0J1xuICogQGltcG9ydCB7UmF3fSBmcm9tICdtZGFzdC11dGlsLXRvLWhhc3QnXG4gKiBAaW1wb3J0IHtTdGF0ZX0gZnJvbSAnLi4vaW5kZXguanMnXG4gKi9cblxuaW1wb3J0IHt0ZXh0fSBmcm9tICcuL3RleHQuanMnXG5cbi8qKlxuICogU2VyaWFsaXplIGEgcmF3IG5vZGUuXG4gKlxuICogQHBhcmFtIHtSYXd9IG5vZGVcbiAqICAgTm9kZSB0byBoYW5kbGUuXG4gKiBAcGFyYW0ge251bWJlciB8IHVuZGVmaW5lZH0gaW5kZXhcbiAqICAgSW5kZXggb2YgYG5vZGVgIGluIGBwYXJlbnQuXG4gKiBAcGFyYW0ge1BhcmVudHMgfCB1bmRlZmluZWR9IHBhcmVudFxuICogICBQYXJlbnQgb2YgYG5vZGVgLlxuICogQHBhcmFtIHtTdGF0ZX0gc3RhdGVcbiAqICAgSW5mbyBwYXNzZWQgYXJvdW5kIGFib3V0IHRoZSBjdXJyZW50IHN0YXRlLlxuICogQHJldHVybnMge3N0cmluZ31cbiAqICAgU2VyaWFsaXplZCBub2RlLlxuICovXG5leHBvcnQgZnVuY3Rpb24gcmF3KG5vZGUsIGluZGV4LCBwYXJlbnQsIHN0YXRlKSB7XG4gIHJldHVybiBzdGF0ZS5zZXR0aW5ncy5hbGxvd0Rhbmdlcm91c0h0bWxcbiAgICA/IG5vZGUudmFsdWVcbiAgICA6IHRleHQobm9kZSwgaW5kZXgsIHBhcmVudCwgc3RhdGUpXG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-html/lib/handle/raw.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-html/lib/handle/root.js":
/*!***********************************************************!*\
  !*** ./node_modules/hast-util-to-html/lib/handle/root.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   root: () => (/* binding */ root)\n/* harmony export */ });\n/**\n * @import {Parents, Root} from 'hast'\n * @import {State} from '../index.js'\n */\n\n/**\n * Serialize a root.\n *\n * @param {Root} node\n *   Node to handle.\n * @param {number | undefined} _1\n *   Index of `node` in `parent.\n * @param {Parents | undefined} _2\n *   Parent of `node`.\n * @param {State} state\n *   Info passed around about the current state.\n * @returns {string}\n *   Serialized node.\n */\nfunction root(node, _1, _2, state) {\n  return state.all(node)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLWh0bWwvbGliL2hhbmRsZS9yb290LmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBLFlBQVksZUFBZTtBQUMzQixZQUFZLE9BQU87QUFDbkI7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsV0FBVyxNQUFNO0FBQ2pCO0FBQ0EsV0FBVyxvQkFBb0I7QUFDL0I7QUFDQSxXQUFXLHFCQUFxQjtBQUNoQztBQUNBLFdBQVcsT0FBTztBQUNsQjtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ087QUFDUDtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGJlbmppXFxEZXNrdG9wXFxHX1BST0dcXE5pY29sYXNcXGJ0ZGFzaC1lY29zeXN0ZW1cXGJ0ZGFzaHNcXG5vZGVfbW9kdWxlc1xcaGFzdC11dGlsLXRvLWh0bWxcXGxpYlxcaGFuZGxlXFxyb290LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGltcG9ydCB7UGFyZW50cywgUm9vdH0gZnJvbSAnaGFzdCdcbiAqIEBpbXBvcnQge1N0YXRlfSBmcm9tICcuLi9pbmRleC5qcydcbiAqL1xuXG4vKipcbiAqIFNlcmlhbGl6ZSBhIHJvb3QuXG4gKlxuICogQHBhcmFtIHtSb290fSBub2RlXG4gKiAgIE5vZGUgdG8gaGFuZGxlLlxuICogQHBhcmFtIHtudW1iZXIgfCB1bmRlZmluZWR9IF8xXG4gKiAgIEluZGV4IG9mIGBub2RlYCBpbiBgcGFyZW50LlxuICogQHBhcmFtIHtQYXJlbnRzIHwgdW5kZWZpbmVkfSBfMlxuICogICBQYXJlbnQgb2YgYG5vZGVgLlxuICogQHBhcmFtIHtTdGF0ZX0gc3RhdGVcbiAqICAgSW5mbyBwYXNzZWQgYXJvdW5kIGFib3V0IHRoZSBjdXJyZW50IHN0YXRlLlxuICogQHJldHVybnMge3N0cmluZ31cbiAqICAgU2VyaWFsaXplZCBub2RlLlxuICovXG5leHBvcnQgZnVuY3Rpb24gcm9vdChub2RlLCBfMSwgXzIsIHN0YXRlKSB7XG4gIHJldHVybiBzdGF0ZS5hbGwobm9kZSlcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-html/lib/handle/root.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-html/lib/handle/text.js":
/*!***********************************************************!*\
  !*** ./node_modules/hast-util-to-html/lib/handle/text.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   text: () => (/* binding */ text)\n/* harmony export */ });\n/* harmony import */ var stringify_entities__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! stringify-entities */ \"(ssr)/./node_modules/stringify-entities/lib/index.js\");\n/**\n * @import {Parents, Text} from 'hast'\n * @import {Raw} from 'mdast-util-to-hast'\n * @import {State} from '../index.js'\n */\n\n\n\n// Declare array as variable so it can be cached by `stringifyEntities`\nconst textEntitySubset = ['<', '&']\n\n/**\n * Serialize a text node.\n *\n * @param {Raw | Text} node\n *   Node to handle.\n * @param {number | undefined} _\n *   Index of `node` in `parent.\n * @param {Parents | undefined} parent\n *   Parent of `node`.\n * @param {State} state\n *   Info passed around about the current state.\n * @returns {string}\n *   Serialized node.\n */\nfunction text(node, _, parent, state) {\n  // Check if content of `node` should be escaped.\n  return parent &&\n    parent.type === 'element' &&\n    (parent.tagName === 'script' || parent.tagName === 'style')\n    ? node.value\n    : (0,stringify_entities__WEBPACK_IMPORTED_MODULE_0__.stringifyEntities)(\n        node.value,\n        Object.assign({}, state.settings.characterReferences, {\n          subset: textEntitySubset\n        })\n      )\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-html/lib/handle/text.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-html/lib/index.js":
/*!*****************************************************!*\
  !*** ./node_modules/hast-util-to-html/lib/index.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   all: () => (/* binding */ all),\n/* harmony export */   toHtml: () => (/* binding */ toHtml)\n/* harmony export */ });\n/* harmony import */ var html_void_elements__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! html-void-elements */ \"(ssr)/./node_modules/html-void-elements/index.js\");\n/* harmony import */ var property_information__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! property-information */ \"(ssr)/./node_modules/property-information/index.js\");\n/* harmony import */ var _handle_index_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./handle/index.js */ \"(ssr)/./node_modules/hast-util-to-html/lib/handle/index.js\");\n/**\n * @import {Nodes, Parents, RootContent} from 'hast'\n * @import {Schema} from 'property-information'\n * @import {Options as StringifyEntitiesOptions} from 'stringify-entities'\n */\n\n/**\n * @typedef {Omit<StringifyEntitiesOptions, 'attribute' | 'escapeOnly' | 'subset'>} CharacterReferences\n *\n * @typedef Options\n *   Configuration.\n * @property {boolean | null | undefined} [allowDangerousCharacters=false]\n *   Do not encode some characters which cause XSS vulnerabilities in older\n *   browsers (default: `false`).\n *\n *   > ⚠️ **Danger**: only set this if you completely trust the content.\n * @property {boolean | null | undefined} [allowDangerousHtml=false]\n *   Allow `raw` nodes and insert them as raw HTML (default: `false`).\n *\n *   When `false`, `Raw` nodes are encoded.\n *\n *   > ⚠️ **Danger**: only set this if you completely trust the content.\n * @property {boolean | null | undefined} [allowParseErrors=false]\n *   Do not encode characters which cause parse errors (even though they work),\n *   to save bytes (default: `false`).\n *\n *   Not used in the SVG space.\n *\n *   > 👉 **Note**: intentionally creates parse errors in markup (how parse\n *   > errors are handled is well defined, so this works but isn’t pretty).\n * @property {boolean | null | undefined} [bogusComments=false]\n *   Use “bogus comments” instead of comments to save byes: `<?charlie>`\n *   instead of `<!--charlie-->` (default: `false`).\n *\n *   > 👉 **Note**: intentionally creates parse errors in markup (how parse\n *   > errors are handled is well defined, so this works but isn’t pretty).\n * @property {CharacterReferences | null | undefined} [characterReferences]\n *   Configure how to serialize character references (optional).\n * @property {boolean | null | undefined} [closeEmptyElements=false]\n *   Close SVG elements without any content with slash (`/`) on the opening tag\n *   instead of an end tag: `<circle />` instead of `<circle></circle>`\n *   (default: `false`).\n *\n *   See `tightSelfClosing` to control whether a space is used before the\n *   slash.\n *\n *   Not used in the HTML space.\n * @property {boolean | null | undefined} [closeSelfClosing=false]\n *   Close self-closing nodes with an extra slash (`/`): `<img />` instead of\n *   `<img>` (default: `false`).\n *\n *   See `tightSelfClosing` to control whether a space is used before the\n *   slash.\n *\n *   Not used in the SVG space.\n * @property {boolean | null | undefined} [collapseEmptyAttributes=false]\n *   Collapse empty attributes: get `class` instead of `class=\"\"` (default:\n *   `false`).\n *\n *   Not used in the SVG space.\n *\n *   > 👉 **Note**: boolean attributes (such as `hidden`) are always collapsed.\n * @property {boolean | null | undefined} [omitOptionalTags=false]\n *   Omit optional opening and closing tags (default: `false`).\n *\n *   For example, in `<ol><li>one</li><li>two</li></ol>`, both `</li>` closing\n *   tags can be omitted.\n *   The first because it’s followed by another `li`, the last because it’s\n *   followed by nothing.\n *\n *   Not used in the SVG space.\n * @property {boolean | null | undefined} [preferUnquoted=false]\n *   Leave attributes unquoted if that results in less bytes (default: `false`).\n *\n *   Not used in the SVG space.\n * @property {boolean | null | undefined} [quoteSmart=false]\n *   Use the other quote if that results in less bytes (default: `false`).\n * @property {Quote | null | undefined} [quote='\"']\n *   Preferred quote to use (default: `'\"'`).\n * @property {Space | null | undefined} [space='html']\n *   When an `<svg>` element is found in the HTML space, this package already\n *   automatically switches to and from the SVG space when entering and exiting\n *   it (default: `'html'`).\n *\n *   > 👉 **Note**: hast is not XML.\n *   > It supports SVG as embedded in HTML.\n *   > It does not support the features available in XML.\n *   > Passing SVG might break but fragments of modern SVG should be fine.\n *   > Use [`xast`][xast] if you need to support SVG as XML.\n * @property {boolean | null | undefined} [tightAttributes=false]\n *   Join attributes together, without whitespace, if possible: get\n *   `class=\"a b\"title=\"c d\"` instead of `class=\"a b\" title=\"c d\"` to save\n *   bytes (default: `false`).\n *\n *   Not used in the SVG space.\n *\n *   > 👉 **Note**: intentionally creates parse errors in markup (how parse\n *   > errors are handled is well defined, so this works but isn’t pretty).\n * @property {boolean | null | undefined} [tightCommaSeparatedLists=false]\n *   Join known comma-separated attribute values with just a comma (`,`),\n *   instead of padding them on the right as well (`,␠`, where `␠` represents a\n *   space) (default: `false`).\n * @property {boolean | null | undefined} [tightDoctype=false]\n *   Drop unneeded spaces in doctypes: `<!doctypehtml>` instead of\n *   `<!doctype html>` to save bytes (default: `false`).\n *\n *   > 👉 **Note**: intentionally creates parse errors in markup (how parse\n *   > errors are handled is well defined, so this works but isn’t pretty).\n * @property {boolean | null | undefined} [tightSelfClosing=false]\n *   Do not use an extra space when closing self-closing elements: `<img/>`\n *   instead of `<img />` (default: `false`).\n *\n *   > 👉 **Note**: only used if `closeSelfClosing: true` or\n *   > `closeEmptyElements: true`.\n * @property {boolean | null | undefined} [upperDoctype=false]\n *   Use a `<!DOCTYPE…` instead of `<!doctype…` (default: `false`).\n *\n *   Useless except for XHTML.\n * @property {ReadonlyArray<string> | null | undefined} [voids]\n *   Tag names of elements to serialize without closing tag (default: `html-void-elements`).\n *\n *   Not used in the SVG space.\n *\n *   > 👉 **Note**: It’s highly unlikely that you want to pass this, because\n *   > hast is not for XML, and HTML will not add more void elements.\n *\n * @typedef {'\"' | \"'\"} Quote\n *   HTML quotes for attribute values.\n *\n * @typedef {Omit<Required<{[key in keyof Options]: Exclude<Options[key], null | undefined>}>, 'space' | 'quote'>} Settings\n *\n * @typedef {'html' | 'svg'} Space\n *   Namespace.\n *\n * @typedef State\n *   Info passed around about the current state.\n * @property {(node: Parents | undefined) => string} all\n *   Serialize the children of a parent node.\n * @property {Quote} alternative\n *   Alternative quote.\n * @property {(node: Nodes, index: number | undefined, parent: Parents | undefined) => string} one\n *   Serialize one node.\n * @property {Quote} quote\n *   Preferred quote.\n * @property {Schema} schema\n *   Current schema.\n * @property {Settings} settings\n *   User configuration.\n */\n\n\n\n\n\n/** @type {Options} */\nconst emptyOptions = {}\n\n/** @type {CharacterReferences} */\nconst emptyCharacterReferences = {}\n\n/** @type {Array<never>} */\nconst emptyChildren = []\n\n/**\n * Serialize hast as HTML.\n *\n * @param {Array<RootContent> | Nodes} tree\n *   Tree to serialize.\n * @param {Options | null | undefined} [options]\n *   Configuration (optional).\n * @returns {string}\n *   Serialized HTML.\n */\nfunction toHtml(tree, options) {\n  const options_ = options || emptyOptions\n  const quote = options_.quote || '\"'\n  const alternative = quote === '\"' ? \"'\" : '\"'\n\n  if (quote !== '\"' && quote !== \"'\") {\n    throw new Error('Invalid quote `' + quote + '`, expected `\\'` or `\"`')\n  }\n\n  /** @type {State} */\n  const state = {\n    one,\n    all,\n    settings: {\n      omitOptionalTags: options_.omitOptionalTags || false,\n      allowParseErrors: options_.allowParseErrors || false,\n      allowDangerousCharacters: options_.allowDangerousCharacters || false,\n      quoteSmart: options_.quoteSmart || false,\n      preferUnquoted: options_.preferUnquoted || false,\n      tightAttributes: options_.tightAttributes || false,\n      upperDoctype: options_.upperDoctype || false,\n      tightDoctype: options_.tightDoctype || false,\n      bogusComments: options_.bogusComments || false,\n      tightCommaSeparatedLists: options_.tightCommaSeparatedLists || false,\n      tightSelfClosing: options_.tightSelfClosing || false,\n      collapseEmptyAttributes: options_.collapseEmptyAttributes || false,\n      allowDangerousHtml: options_.allowDangerousHtml || false,\n      voids: options_.voids || html_void_elements__WEBPACK_IMPORTED_MODULE_0__.htmlVoidElements,\n      characterReferences:\n        options_.characterReferences || emptyCharacterReferences,\n      closeSelfClosing: options_.closeSelfClosing || false,\n      closeEmptyElements: options_.closeEmptyElements || false\n    },\n    schema: options_.space === 'svg' ? property_information__WEBPACK_IMPORTED_MODULE_1__.svg : property_information__WEBPACK_IMPORTED_MODULE_1__.html,\n    quote,\n    alternative\n  }\n\n  return state.one(\n    Array.isArray(tree) ? {type: 'root', children: tree} : tree,\n    undefined,\n    undefined\n  )\n}\n\n/**\n * Serialize a node.\n *\n * @this {State}\n *   Info passed around about the current state.\n * @param {Nodes} node\n *   Node to handle.\n * @param {number | undefined} index\n *   Index of `node` in `parent.\n * @param {Parents | undefined} parent\n *   Parent of `node`.\n * @returns {string}\n *   Serialized node.\n */\nfunction one(node, index, parent) {\n  return (0,_handle_index_js__WEBPACK_IMPORTED_MODULE_2__.handle)(node, index, parent, this)\n}\n\n/**\n * Serialize all children of `parent`.\n *\n * @this {State}\n *   Info passed around about the current state.\n * @param {Parents | undefined} parent\n *   Parent whose children to serialize.\n * @returns {string}\n */\nfunction all(parent) {\n  /** @type {Array<string>} */\n  const results = []\n  const children = (parent && parent.children) || emptyChildren\n  let index = -1\n\n  while (++index < children.length) {\n    results[index] = this.one(children[index], index, parent)\n  }\n\n  return results.join('')\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-html/lib/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-html/lib/omission/closing.js":
/*!****************************************************************!*\
  !*** ./node_modules/hast-util-to-html/lib/omission/closing.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   closing: () => (/* binding */ closing)\n/* harmony export */ });\n/* harmony import */ var hast_util_whitespace__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! hast-util-whitespace */ \"(ssr)/./node_modules/hast-util-whitespace/lib/index.js\");\n/* harmony import */ var _util_siblings_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./util/siblings.js */ \"(ssr)/./node_modules/hast-util-to-html/lib/omission/util/siblings.js\");\n/* harmony import */ var _omission_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./omission.js */ \"(ssr)/./node_modules/hast-util-to-html/lib/omission/omission.js\");\n/**\n * @import {Element, Parents} from 'hast'\n */\n\n\n\n\n\nconst closing = (0,_omission_js__WEBPACK_IMPORTED_MODULE_0__.omission)({\n  body,\n  caption: headOrColgroupOrCaption,\n  colgroup: headOrColgroupOrCaption,\n  dd,\n  dt,\n  head: headOrColgroupOrCaption,\n  html,\n  li,\n  optgroup,\n  option,\n  p,\n  rp: rubyElement,\n  rt: rubyElement,\n  tbody,\n  td: cells,\n  tfoot,\n  th: cells,\n  thead,\n  tr\n})\n\n/**\n * Macro for `</head>`, `</colgroup>`, and `</caption>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parents | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction headOrColgroupOrCaption(_, index, parent) {\n  const next = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(parent, index, true)\n  return (\n    !next ||\n    (next.type !== 'comment' &&\n      !(next.type === 'text' && (0,hast_util_whitespace__WEBPACK_IMPORTED_MODULE_2__.whitespace)(next.value.charAt(0))))\n  )\n}\n\n/**\n * Whether to omit `</html>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parents | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction html(_, index, parent) {\n  const next = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(parent, index)\n  return !next || next.type !== 'comment'\n}\n\n/**\n * Whether to omit `</body>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parents | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction body(_, index, parent) {\n  const next = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(parent, index)\n  return !next || next.type !== 'comment'\n}\n\n/**\n * Whether to omit `</p>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parents | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction p(_, index, parent) {\n  const next = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(parent, index)\n  return next\n    ? next.type === 'element' &&\n        (next.tagName === 'address' ||\n          next.tagName === 'article' ||\n          next.tagName === 'aside' ||\n          next.tagName === 'blockquote' ||\n          next.tagName === 'details' ||\n          next.tagName === 'div' ||\n          next.tagName === 'dl' ||\n          next.tagName === 'fieldset' ||\n          next.tagName === 'figcaption' ||\n          next.tagName === 'figure' ||\n          next.tagName === 'footer' ||\n          next.tagName === 'form' ||\n          next.tagName === 'h1' ||\n          next.tagName === 'h2' ||\n          next.tagName === 'h3' ||\n          next.tagName === 'h4' ||\n          next.tagName === 'h5' ||\n          next.tagName === 'h6' ||\n          next.tagName === 'header' ||\n          next.tagName === 'hgroup' ||\n          next.tagName === 'hr' ||\n          next.tagName === 'main' ||\n          next.tagName === 'menu' ||\n          next.tagName === 'nav' ||\n          next.tagName === 'ol' ||\n          next.tagName === 'p' ||\n          next.tagName === 'pre' ||\n          next.tagName === 'section' ||\n          next.tagName === 'table' ||\n          next.tagName === 'ul')\n    : !parent ||\n        // Confusing parent.\n        !(\n          parent.type === 'element' &&\n          (parent.tagName === 'a' ||\n            parent.tagName === 'audio' ||\n            parent.tagName === 'del' ||\n            parent.tagName === 'ins' ||\n            parent.tagName === 'map' ||\n            parent.tagName === 'noscript' ||\n            parent.tagName === 'video')\n        )\n}\n\n/**\n * Whether to omit `</li>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parents | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction li(_, index, parent) {\n  const next = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(parent, index)\n  return !next || (next.type === 'element' && next.tagName === 'li')\n}\n\n/**\n * Whether to omit `</dt>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parents | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction dt(_, index, parent) {\n  const next = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(parent, index)\n  return Boolean(\n    next &&\n      next.type === 'element' &&\n      (next.tagName === 'dt' || next.tagName === 'dd')\n  )\n}\n\n/**\n * Whether to omit `</dd>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parents | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction dd(_, index, parent) {\n  const next = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(parent, index)\n  return (\n    !next ||\n    (next.type === 'element' &&\n      (next.tagName === 'dt' || next.tagName === 'dd'))\n  )\n}\n\n/**\n * Whether to omit `</rt>` or `</rp>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parents | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction rubyElement(_, index, parent) {\n  const next = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(parent, index)\n  return (\n    !next ||\n    (next.type === 'element' &&\n      (next.tagName === 'rp' || next.tagName === 'rt'))\n  )\n}\n\n/**\n * Whether to omit `</optgroup>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parents | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction optgroup(_, index, parent) {\n  const next = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(parent, index)\n  return !next || (next.type === 'element' && next.tagName === 'optgroup')\n}\n\n/**\n * Whether to omit `</option>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parents | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction option(_, index, parent) {\n  const next = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(parent, index)\n  return (\n    !next ||\n    (next.type === 'element' &&\n      (next.tagName === 'option' || next.tagName === 'optgroup'))\n  )\n}\n\n/**\n * Whether to omit `</thead>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parents | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction thead(_, index, parent) {\n  const next = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(parent, index)\n  return Boolean(\n    next &&\n      next.type === 'element' &&\n      (next.tagName === 'tbody' || next.tagName === 'tfoot')\n  )\n}\n\n/**\n * Whether to omit `</tbody>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parents | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction tbody(_, index, parent) {\n  const next = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(parent, index)\n  return (\n    !next ||\n    (next.type === 'element' &&\n      (next.tagName === 'tbody' || next.tagName === 'tfoot'))\n  )\n}\n\n/**\n * Whether to omit `</tfoot>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parents | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction tfoot(_, index, parent) {\n  return !(0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(parent, index)\n}\n\n/**\n * Whether to omit `</tr>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parents | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction tr(_, index, parent) {\n  const next = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(parent, index)\n  return !next || (next.type === 'element' && next.tagName === 'tr')\n}\n\n/**\n * Whether to omit `</td>` or `</th>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parents | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction cells(_, index, parent) {\n  const next = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(parent, index)\n  return (\n    !next ||\n    (next.type === 'element' &&\n      (next.tagName === 'td' || next.tagName === 'th'))\n  )\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-html/lib/omission/closing.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-html/lib/omission/omission.js":
/*!*****************************************************************!*\
  !*** ./node_modules/hast-util-to-html/lib/omission/omission.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   omission: () => (/* binding */ omission)\n/* harmony export */ });\n/**\n * @import {Element, Parents} from 'hast'\n */\n\n/**\n * @callback OmitHandle\n *   Check if a tag can be omitted.\n * @param {Element} element\n *   Element to check.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parents | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether to omit a tag.\n *\n */\n\nconst own = {}.hasOwnProperty\n\n/**\n * Factory to check if a given node can have a tag omitted.\n *\n * @param {Record<string, OmitHandle>} handlers\n *   Omission handlers, where each key is a tag name, and each value is the\n *   corresponding handler.\n * @returns {OmitHandle}\n *   Whether to omit a tag of an element.\n */\nfunction omission(handlers) {\n  return omit\n\n  /**\n   * Check if a given node can have a tag omitted.\n   *\n   * @type {OmitHandle}\n   */\n  function omit(node, index, parent) {\n    return (\n      own.call(handlers, node.tagName) &&\n      handlers[node.tagName](node, index, parent)\n    )\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-html/lib/omission/omission.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-html/lib/omission/opening.js":
/*!****************************************************************!*\
  !*** ./node_modules/hast-util-to-html/lib/omission/opening.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   opening: () => (/* binding */ opening)\n/* harmony export */ });\n/* harmony import */ var hast_util_whitespace__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! hast-util-whitespace */ \"(ssr)/./node_modules/hast-util-whitespace/lib/index.js\");\n/* harmony import */ var _util_siblings_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./util/siblings.js */ \"(ssr)/./node_modules/hast-util-to-html/lib/omission/util/siblings.js\");\n/* harmony import */ var _closing_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./closing.js */ \"(ssr)/./node_modules/hast-util-to-html/lib/omission/closing.js\");\n/* harmony import */ var _omission_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./omission.js */ \"(ssr)/./node_modules/hast-util-to-html/lib/omission/omission.js\");\n/**\n * @import {Element, Parents} from 'hast'\n */\n\n\n\n\n\n\nconst opening = (0,_omission_js__WEBPACK_IMPORTED_MODULE_0__.omission)({\n  body,\n  colgroup,\n  head,\n  html,\n  tbody\n})\n\n/**\n * Whether to omit `<html>`.\n *\n * @param {Element} node\n *   Element.\n * @returns {boolean}\n *   Whether the opening tag can be omitted.\n */\nfunction html(node) {\n  const head = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(node, -1)\n  return !head || head.type !== 'comment'\n}\n\n/**\n * Whether to omit `<head>`.\n *\n * @param {Element} node\n *   Element.\n * @returns {boolean}\n *   Whether the opening tag can be omitted.\n */\nfunction head(node) {\n  /** @type {Set<string>} */\n  const seen = new Set()\n\n  // Whether `srcdoc` or not,\n  // make sure the content model at least doesn’t have too many `base`s/`title`s.\n  for (const child of node.children) {\n    if (\n      child.type === 'element' &&\n      (child.tagName === 'base' || child.tagName === 'title')\n    ) {\n      if (seen.has(child.tagName)) return false\n      seen.add(child.tagName)\n    }\n  }\n\n  // “May be omitted if the element is empty,\n  // or if the first thing inside the head element is an element.”\n  const child = node.children[0]\n  return !child || child.type === 'element'\n}\n\n/**\n * Whether to omit `<body>`.\n *\n * @param {Element} node\n *   Element.\n * @returns {boolean}\n *   Whether the opening tag can be omitted.\n */\nfunction body(node) {\n  const head = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(node, -1, true)\n\n  return (\n    !head ||\n    (head.type !== 'comment' &&\n      !(head.type === 'text' && (0,hast_util_whitespace__WEBPACK_IMPORTED_MODULE_2__.whitespace)(head.value.charAt(0))) &&\n      !(\n        head.type === 'element' &&\n        (head.tagName === 'meta' ||\n          head.tagName === 'link' ||\n          head.tagName === 'script' ||\n          head.tagName === 'style' ||\n          head.tagName === 'template')\n      ))\n  )\n}\n\n/**\n * Whether to omit `<colgroup>`.\n * The spec describes some logic for the opening tag, but it’s easier to\n * implement in the closing tag, to the same effect, so we handle it there\n * instead.\n *\n * @param {Element} node\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parents | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the opening tag can be omitted.\n */\nfunction colgroup(node, index, parent) {\n  const previous = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingBefore)(parent, index)\n  const head = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(node, -1, true)\n\n  // Previous colgroup was already omitted.\n  if (\n    parent &&\n    previous &&\n    previous.type === 'element' &&\n    previous.tagName === 'colgroup' &&\n    (0,_closing_js__WEBPACK_IMPORTED_MODULE_3__.closing)(previous, parent.children.indexOf(previous), parent)\n  ) {\n    return false\n  }\n\n  return Boolean(head && head.type === 'element' && head.tagName === 'col')\n}\n\n/**\n * Whether to omit `<tbody>`.\n *\n * @param {Element} node\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parents | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the opening tag can be omitted.\n */\nfunction tbody(node, index, parent) {\n  const previous = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingBefore)(parent, index)\n  const head = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(node, -1)\n\n  // Previous table section was already omitted.\n  if (\n    parent &&\n    previous &&\n    previous.type === 'element' &&\n    (previous.tagName === 'thead' || previous.tagName === 'tbody') &&\n    (0,_closing_js__WEBPACK_IMPORTED_MODULE_3__.closing)(previous, parent.children.indexOf(previous), parent)\n  ) {\n    return false\n  }\n\n  return Boolean(head && head.type === 'element' && head.tagName === 'tr')\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-html/lib/omission/opening.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-html/lib/omission/util/siblings.js":
/*!**********************************************************************!*\
  !*** ./node_modules/hast-util-to-html/lib/omission/util/siblings.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   siblingAfter: () => (/* binding */ siblingAfter),\n/* harmony export */   siblingBefore: () => (/* binding */ siblingBefore)\n/* harmony export */ });\n/* harmony import */ var hast_util_whitespace__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! hast-util-whitespace */ \"(ssr)/./node_modules/hast-util-whitespace/lib/index.js\");\n/**\n * @import {Parents, RootContent} from 'hast'\n */\n\n\n\nconst siblingAfter = siblings(1)\nconst siblingBefore = siblings(-1)\n\n/** @type {Array<RootContent>} */\nconst emptyChildren = []\n\n/**\n * Factory to check siblings in a direction.\n *\n * @param {number} increment\n */\nfunction siblings(increment) {\n  return sibling\n\n  /**\n   * Find applicable siblings in a direction.\n   *\n   * @template {Parents} Parent\n   *   Parent type.\n   * @param {Parent | undefined} parent\n   *   Parent.\n   * @param {number | undefined} index\n   *   Index of child in `parent`.\n   * @param {boolean | undefined} [includeWhitespace=false]\n   *   Whether to include whitespace (default: `false`).\n   * @returns {Parent extends {children: Array<infer Child>} ? Child | undefined : never}\n   *   Child of parent.\n   */\n  function sibling(parent, index, includeWhitespace) {\n    const siblings = parent ? parent.children : emptyChildren\n    let offset = (index || 0) + increment\n    let next = siblings[offset]\n\n    if (!includeWhitespace) {\n      while (next && (0,hast_util_whitespace__WEBPACK_IMPORTED_MODULE_0__.whitespace)(next)) {\n        offset += increment\n        next = siblings[offset]\n      }\n    }\n\n    // @ts-expect-error: it’s a correct child.\n    return next\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-html/lib/omission/util/siblings.js\n");

/***/ })

};
;