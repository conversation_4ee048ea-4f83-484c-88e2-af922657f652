"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/hast-util-sanitize";
exports.ids = ["vendor-chunks/hast-util-sanitize"];
exports.modules = {

/***/ "(ssr)/./node_modules/hast-util-sanitize/lib/index.js":
/*!******************************************************!*\
  !*** ./node_modules/hast-util-sanitize/lib/index.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   sanitize: () => (/* binding */ sanitize)\n/* harmony export */ });\n/* harmony import */ var _ungap_structured_clone__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @ungap/structured-clone */ \"(ssr)/./node_modules/@ungap/structured-clone/esm/index.js\");\n/* harmony import */ var unist_util_position__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! unist-util-position */ \"(ssr)/./node_modules/unist-util-position/lib/index.js\");\n/* harmony import */ var _schema_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./schema.js */ \"(ssr)/./node_modules/hast-util-sanitize/lib/schema.js\");\n/**\n * @import {\n *   Comment,\n *   Doctype,\n *   ElementContent,\n *   Element,\n *   Nodes,\n *   Properties,\n *   RootContent,\n *   Root,\n *   Text\n * } from 'hast'\n */\n\n/**\n * @typedef {[string, ...Array<Exclude<Properties[keyof Properties], Array<any>> | RegExp>] | string} PropertyDefinition\n *   Definition for a property.\n *\n * @typedef Schema\n *   Schema that defines what nodes and properties are allowed.\n *\n *   The default schema is `defaultSchema`, which follows how GitHub cleans.\n *   If any top-level key is missing in the given schema, the corresponding\n *   value of the default schema is used.\n *\n *   To extend the standard schema with a few changes, clone `defaultSchema`\n *   like so:\n *\n *   ```js\n *   import deepmerge from 'deepmerge'\n *   import {h} from 'hastscript'\n *   import {defaultSchema, sanitize} from 'hast-util-sanitize'\n *\n *   // This allows `className` on all elements.\n *   const schema = deepmerge(defaultSchema, {attributes: {'*': ['className']}})\n *\n *   const tree = sanitize(h('div', {className: ['foo']}), schema)\n *\n *   // `tree` still has `className`.\n *   console.log(tree)\n *   // {\n *   //   type: 'element',\n *   //   tagName: 'div',\n *   //   properties: {className: ['foo']},\n *   //   children: []\n *   // }\n *   ```\n * @property {boolean | null | undefined} [allowComments=false]\n *   Whether to allow comment nodes (default: `false`).\n *\n *   For example:\n *\n *   ```js\n *   allowComments: true\n *   ```\n * @property {boolean | null | undefined} [allowDoctypes=false]\n *   Whether to allow doctype nodes (default: `false`).\n *\n *   For example:\n *\n *   ```js\n *   allowDoctypes: true\n *   ```\n * @property {Record<string, Array<string>> | null | undefined} [ancestors]\n *   Map of tag names to a list of tag names which are required ancestors\n *   (default: `defaultSchema.ancestors`).\n *\n *   Elements with these tag names will be ignored if they occur outside of one\n *   of their allowed parents.\n *\n *   For example:\n *\n *   ```js\n *   ancestors: {\n *     tbody: ['table'],\n *     // …\n *     tr: ['table']\n *   }\n *   ```\n * @property {Record<string, Array<PropertyDefinition>> | null | undefined} [attributes]\n *   Map of tag names to allowed property names (default:\n *   `defaultSchema.attributes`).\n *\n *   The special key `'*'` as a tag name defines property names allowed on all\n *   elements.\n *\n *   The special value `'data*'` as a property name can be used to allow all\n *   `data` properties.\n *\n *   For example:\n *\n *   ```js\n *   attributes: {\n *     'ariaDescribedBy', 'ariaLabel', 'ariaLabelledBy', …, 'href'\n *     // …\n *     '*': [\n *       'abbr',\n *       'accept',\n *       'acceptCharset',\n *       // …\n *       'vAlign',\n *       'value',\n *       'width'\n *     ]\n *   }\n *   ```\n *\n *   Instead of a single string in the array, which allows any property value\n *   for the field, you can use an array to allow several values.\n *   For example, `input: ['type']` allows `type` set to any value on `input`s.\n *   But `input: [['type', 'checkbox', 'radio']]` allows `type` when set to\n *   `'checkbox'` or `'radio'`.\n *\n *   You can use regexes, so for example `span: [['className', /^hljs-/]]`\n *   allows any class that starts with `hljs-` on `span`s.\n *\n *   When comma- or space-separated values are used (such as `className`), each\n *   value in is checked individually.\n *   For example, to allow certain classes on `span`s for syntax highlighting,\n *   use `span: [['className', 'number', 'operator', 'token']]`.\n *   This will allow `'number'`, `'operator'`, and `'token'` classes, but drop\n *   others.\n * @property {Array<string> | null | undefined} [clobber]\n *   List of property names that clobber (default: `defaultSchema.clobber`).\n *\n *   For example:\n *\n *   ```js\n *   clobber: ['ariaDescribedBy', 'ariaLabelledBy', 'id', 'name']\n *   ```\n * @property {string | null | undefined} [clobberPrefix]\n *   Prefix to use before clobbering properties (default:\n *   `defaultSchema.clobberPrefix`).\n *\n *   For example:\n *\n *   ```js\n *   clobberPrefix: 'user-content-'\n *   ```\n * @property {Record<string, Array<string> | null | undefined> | null | undefined} [protocols]\n *   Map of *property names* to allowed protocols (default:\n *   `defaultSchema.protocols`).\n *\n *   This defines URLs that are always allowed to have local URLs (relative to\n *   the current website, such as `this`, `#this`, `/this`, or `?this`), and\n *   only allowed to have remote URLs (such as `https://example.com`) if they\n *   use a known protocol.\n *\n *   For example:\n *\n *   ```js\n *   protocols: {\n *     cite: ['http', 'https'],\n *     // …\n *     src: ['http', 'https']\n *   }\n *   ```\n * @property {Record<string, Record<string, Properties[keyof Properties]>> | null | undefined} [required]\n *   Map of tag names to required property names with a default value\n *   (default: `defaultSchema.required`).\n *\n *   This defines properties that must be set.\n *   If a field does not exist (after the element was made safe), these will be\n *   added with the given value.\n *\n *   For example:\n *\n *   ```js\n *   required: {\n *     input: {disabled: true, type: 'checkbox'}\n *   }\n *   ```\n *\n *   > 👉 **Note**: properties are first checked based on `schema.attributes`,\n *   > then on `schema.required`.\n *   > That means properties could be removed by `attributes` and then added\n *   > again with `required`.\n * @property {Array<string> | null | undefined} [strip]\n *   List of tag names to strip from the tree (default: `defaultSchema.strip`).\n *\n *   By default, unsafe elements (those not in `schema.tagNames`) are replaced\n *   by what they contain.\n *   This option can drop their contents.\n *\n *   For example:\n *\n *   ```js\n *   strip: ['script']\n *   ```\n * @property {Array<string> | null | undefined} [tagNames]\n *   List of allowed tag names (default: `defaultSchema.tagNames`).\n *\n *   For example:\n *\n *   ```js\n *   tagNames: [\n *     'a',\n *     'b',\n *     // …\n *     'ul',\n *     'var'\n *   ]\n *   ```\n *\n * @typedef State\n *   Info passed around.\n * @property {Readonly<Schema>} schema\n *   Schema.\n * @property {Array<string>} stack\n *   Tag names of ancestors.\n */\n\n\n\n\n\nconst own = {}.hasOwnProperty\n\n/**\n * Sanitize a tree.\n *\n * @param {Readonly<Nodes>} node\n *   Unsafe tree.\n * @param {Readonly<Schema> | null | undefined} [options]\n *   Configuration (default: `defaultSchema`).\n * @returns {Nodes}\n *   New, safe tree.\n */\nfunction sanitize(node, options) {\n  /** @type {Nodes} */\n  let result = {type: 'root', children: []}\n\n  /** @type {State} */\n  const state = {\n    schema: options ? {..._schema_js__WEBPACK_IMPORTED_MODULE_0__.defaultSchema, ...options} : _schema_js__WEBPACK_IMPORTED_MODULE_0__.defaultSchema,\n    stack: []\n  }\n  const replace = transform(state, node)\n\n  if (replace) {\n    if (Array.isArray(replace)) {\n      if (replace.length === 1) {\n        result = replace[0]\n      } else {\n        result.children = replace\n      }\n    } else {\n      result = replace\n    }\n  }\n\n  return result\n}\n\n/**\n * Sanitize `node`.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Readonly<unknown>} node\n *   Unsafe node.\n * @returns {Array<ElementContent> | Nodes | undefined}\n *   Safe result.\n */\nfunction transform(state, node) {\n  if (node && typeof node === 'object') {\n    const unsafe = /** @type {Record<string, Readonly<unknown>>} */ (node)\n    const type = typeof unsafe.type === 'string' ? unsafe.type : ''\n\n    switch (type) {\n      case 'comment': {\n        return comment(state, unsafe)\n      }\n\n      case 'doctype': {\n        return doctype(state, unsafe)\n      }\n\n      case 'element': {\n        return element(state, unsafe)\n      }\n\n      case 'root': {\n        return root(state, unsafe)\n      }\n\n      case 'text': {\n        return text(state, unsafe)\n      }\n\n      default:\n    }\n  }\n}\n\n/**\n * Make a safe comment.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Readonly<Record<string, Readonly<unknown>>>} unsafe\n *   Unsafe comment-like value.\n * @returns {Comment | undefined}\n *   Safe comment (if with `allowComments`).\n */\nfunction comment(state, unsafe) {\n  if (state.schema.allowComments) {\n    // See <https://html.spec.whatwg.org/multipage/parsing.html#serialising-html-fragments>\n    const result = typeof unsafe.value === 'string' ? unsafe.value : ''\n    const index = result.indexOf('-->')\n    const value = index < 0 ? result : result.slice(0, index)\n\n    /** @type {Comment} */\n    const node = {type: 'comment', value}\n\n    patch(node, unsafe)\n\n    return node\n  }\n}\n\n/**\n * Make a safe doctype.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Readonly<Record<string, Readonly<unknown>>>} unsafe\n *   Unsafe doctype-like value.\n * @returns {Doctype | undefined}\n *   Safe doctype (if with `allowDoctypes`).\n */\nfunction doctype(state, unsafe) {\n  if (state.schema.allowDoctypes) {\n    /** @type {Doctype} */\n    const node = {type: 'doctype'}\n\n    patch(node, unsafe)\n\n    return node\n  }\n}\n\n/**\n * Make a safe element.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Readonly<Record<string, Readonly<unknown>>>} unsafe\n *   Unsafe element-like value.\n * @returns {Array<ElementContent> | Element | undefined}\n *   Safe element.\n */\nfunction element(state, unsafe) {\n  const name = typeof unsafe.tagName === 'string' ? unsafe.tagName : ''\n\n  state.stack.push(name)\n\n  const content = /** @type {Array<ElementContent>} */ (\n    children(state, unsafe.children)\n  )\n  const properties_ = properties(state, unsafe.properties)\n\n  state.stack.pop()\n\n  let safeElement = false\n\n  if (\n    name &&\n    name !== '*' &&\n    (!state.schema.tagNames || state.schema.tagNames.includes(name))\n  ) {\n    safeElement = true\n\n    // Some nodes can break out of their context if they don’t have a certain\n    // ancestor.\n    if (state.schema.ancestors && own.call(state.schema.ancestors, name)) {\n      const ancestors = state.schema.ancestors[name]\n      let index = -1\n\n      safeElement = false\n\n      while (++index < ancestors.length) {\n        if (state.stack.includes(ancestors[index])) {\n          safeElement = true\n        }\n      }\n    }\n  }\n\n  if (!safeElement) {\n    return state.schema.strip && !state.schema.strip.includes(name)\n      ? content\n      : undefined\n  }\n\n  /** @type {Element} */\n  const node = {\n    type: 'element',\n    tagName: name,\n    properties: properties_,\n    children: content\n  }\n\n  patch(node, unsafe)\n\n  return node\n}\n\n/**\n * Make a safe root.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Readonly<Record<string, Readonly<unknown>>>} unsafe\n *   Unsafe root-like value.\n * @returns {Root}\n *   Safe root.\n */\nfunction root(state, unsafe) {\n  const content = /** @type {Array<RootContent>} */ (\n    children(state, unsafe.children)\n  )\n\n  /** @type {Root} */\n  const node = {type: 'root', children: content}\n\n  patch(node, unsafe)\n\n  return node\n}\n\n/**\n * Make a safe text.\n *\n * @param {State} _\n *   Info passed around.\n * @param {Readonly<Record<string, Readonly<unknown>>>} unsafe\n *   Unsafe text-like value.\n * @returns {Text}\n *   Safe text.\n */\nfunction text(_, unsafe) {\n  const value = typeof unsafe.value === 'string' ? unsafe.value : ''\n  /** @type {Text} */\n  const node = {type: 'text', value}\n\n  patch(node, unsafe)\n\n  return node\n}\n\n/**\n * Make children safe.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Readonly<unknown>} children\n *   Unsafe value.\n * @returns {Array<Nodes>}\n *   Safe children.\n */\nfunction children(state, children) {\n  /** @type {Array<Nodes>} */\n  const results = []\n\n  if (Array.isArray(children)) {\n    const childrenUnknown = /** @type {Array<Readonly<unknown>>} */ (children)\n    let index = -1\n\n    while (++index < childrenUnknown.length) {\n      const value = transform(state, childrenUnknown[index])\n\n      if (value) {\n        if (Array.isArray(value)) {\n          results.push(...value)\n        } else {\n          results.push(value)\n        }\n      }\n    }\n  }\n\n  return results\n}\n\n/**\n * Make element properties safe.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Readonly<unknown>} properties\n *   Unsafe value.\n * @returns {Properties}\n *   Safe value.\n */\nfunction properties(state, properties) {\n  const tagName = state.stack[state.stack.length - 1]\n  const attributes = state.schema.attributes\n  const required = state.schema.required\n  const specific =\n    attributes && own.call(attributes, tagName)\n      ? attributes[tagName]\n      : undefined\n  const defaults =\n    attributes && own.call(attributes, '*') ? attributes['*'] : undefined\n  const properties_ =\n    /** @type {Readonly<Record<string, Readonly<unknown>>>} */ (\n      properties && typeof properties === 'object' ? properties : {}\n    )\n  /** @type {Properties} */\n  const result = {}\n  /** @type {string} */\n  let key\n\n  for (key in properties_) {\n    if (own.call(properties_, key)) {\n      const unsafe = properties_[key]\n      let safe = propertyValue(\n        state,\n        findDefinition(specific, key),\n        key,\n        unsafe\n      )\n\n      if (safe === null || safe === undefined) {\n        safe = propertyValue(state, findDefinition(defaults, key), key, unsafe)\n      }\n\n      if (safe !== null && safe !== undefined) {\n        result[key] = safe\n      }\n    }\n  }\n\n  if (required && own.call(required, tagName)) {\n    const properties = required[tagName]\n\n    for (key in properties) {\n      if (own.call(properties, key) && !own.call(result, key)) {\n        result[key] = properties[key]\n      }\n    }\n  }\n\n  return result\n}\n\n/**\n * Sanitize a property value.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Readonly<PropertyDefinition> | undefined} definition\n *   Definition.\n * @param {string} key\n *   Field name.\n * @param {Readonly<unknown>} value\n *   Unsafe value (but an array).\n * @returns {Array<number | string> | boolean | number | string | undefined}\n *   Safe value.\n */\nfunction propertyValue(state, definition, key, value) {\n  return definition\n    ? Array.isArray(value)\n      ? propertyValueMany(state, definition, key, value)\n      : propertyValuePrimitive(state, definition, key, value)\n    : undefined\n}\n\n/**\n * Sanitize a property value which is a list.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Readonly<PropertyDefinition>} definition\n *   Definition.\n * @param {string} key\n *   Field name.\n * @param {Readonly<Array<Readonly<unknown>>>} values\n *   Unsafe value (but an array).\n * @returns {Array<number | string>}\n *   Safe value.\n */\nfunction propertyValueMany(state, definition, key, values) {\n  let index = -1\n  /** @type {Array<number | string>} */\n  const result = []\n\n  while (++index < values.length) {\n    const value = propertyValuePrimitive(state, definition, key, values[index])\n\n    if (typeof value === 'number' || typeof value === 'string') {\n      result.push(value)\n    }\n  }\n\n  return result\n}\n\n/**\n * Sanitize a property value which is a primitive.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Readonly<PropertyDefinition>} definition\n *   Definition.\n * @param {string} key\n *   Field name.\n * @param {Readonly<unknown>} value\n *   Unsafe value (but not an array).\n * @returns {boolean | number | string | undefined}\n *   Safe value.\n */\nfunction propertyValuePrimitive(state, definition, key, value) {\n  if (\n    typeof value !== 'boolean' &&\n    typeof value !== 'number' &&\n    typeof value !== 'string'\n  ) {\n    return\n  }\n\n  if (!safeProtocol(state, key, value)) {\n    return\n  }\n\n  // Just a string, or only one item in an array, means all values are OK.\n  // More than one item means an allow list.\n  if (typeof definition === 'object' && definition.length > 1) {\n    let ok = false\n    let index = 0 // Ignore `key`, which is the first item.\n\n    while (++index < definition.length) {\n      const allowed = definition[index]\n\n      // Expression.\n      if (allowed && typeof allowed === 'object' && 'flags' in allowed) {\n        if (allowed.test(String(value))) {\n          ok = true\n          break\n        }\n      }\n      // Primitive.\n      else if (allowed === value) {\n        ok = true\n        break\n      }\n    }\n\n    if (!ok) return\n  }\n\n  return state.schema.clobber &&\n    state.schema.clobberPrefix &&\n    state.schema.clobber.includes(key)\n    ? state.schema.clobberPrefix + value\n    : value\n}\n\n/**\n * Check whether `value` is a safe URL.\n *\n * @param {State} state\n *   Info passed around.\n * @param {string} key\n *   Field name.\n * @param {Readonly<unknown>} value\n *   Unsafe value.\n * @returns {boolean}\n *   Whether it’s a safe value.\n */\nfunction safeProtocol(state, key, value) {\n  const protocols =\n    state.schema.protocols && own.call(state.schema.protocols, key)\n      ? state.schema.protocols[key]\n      : undefined\n\n  // No protocols defined? Then everything is fine.\n  if (!protocols || protocols.length === 0) {\n    return true\n  }\n\n  const url = String(value)\n  const colon = url.indexOf(':')\n  const questionMark = url.indexOf('?')\n  const numberSign = url.indexOf('#')\n  const slash = url.indexOf('/')\n\n  if (\n    colon < 0 ||\n    // If the first colon is after a `?`, `#`, or `/`, it’s not a protocol.\n    (slash > -1 && colon > slash) ||\n    (questionMark > -1 && colon > questionMark) ||\n    (numberSign > -1 && colon > numberSign)\n  ) {\n    return true\n  }\n\n  let index = -1\n\n  while (++index < protocols.length) {\n    const protocol = protocols[index]\n\n    if (\n      colon === protocol.length &&\n      url.slice(0, protocol.length) === protocol\n    ) {\n      return true\n    }\n  }\n\n  return false\n}\n\n/**\n * Add data and position.\n *\n * @param {Nodes} node\n *   Node to patch safe data and position on.\n * @param {Readonly<Record<string, Readonly<unknown>>>} unsafe\n *   Unsafe node-like value.\n * @returns {undefined}\n *   Nothing.\n */\nfunction patch(node, unsafe) {\n  const cleanPosition = (0,unist_util_position__WEBPACK_IMPORTED_MODULE_1__.position)(\n    // @ts-expect-error: looks like a node.\n    unsafe\n  )\n\n  if (unsafe.data) {\n    node.data = (0,_ungap_structured_clone__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(unsafe.data)\n  }\n\n  if (cleanPosition) node.position = cleanPosition\n}\n\n/**\n *\n * @param {Readonly<Array<PropertyDefinition>> | undefined} definitions\n * @param {string} key\n * @returns {Readonly<PropertyDefinition> | undefined}\n */\nfunction findDefinition(definitions, key) {\n  /** @type {PropertyDefinition | undefined} */\n  let dataDefault\n  let index = -1\n\n  if (definitions) {\n    while (++index < definitions.length) {\n      const entry = definitions[index]\n      const name = typeof entry === 'string' ? entry : entry[0]\n\n      if (name === key) {\n        return entry\n      }\n\n      if (name === 'data*') dataDefault = entry\n    }\n  }\n\n  if (key.length > 4 && key.slice(0, 4).toLowerCase() === 'data') {\n    return dataDefault\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-sanitize/lib/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-sanitize/lib/schema.js":
/*!*******************************************************!*\
  !*** ./node_modules/hast-util-sanitize/lib/schema.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaultSchema: () => (/* binding */ defaultSchema)\n/* harmony export */ });\n/**\n * @import {Schema} from 'hast-util-sanitize'\n */\n\n// Couple of ARIA attributes allowed in several, but not all, places.\nconst aria = ['ariaDescribedBy', 'ariaLabel', 'ariaLabelledBy']\n\n/**\n * Default schema.\n *\n * Follows GitHub style sanitation.\n *\n * @type {Schema}\n */\nconst defaultSchema = {\n  ancestors: {\n    tbody: ['table'],\n    td: ['table'],\n    th: ['table'],\n    thead: ['table'],\n    tfoot: ['table'],\n    tr: ['table']\n  },\n  attributes: {\n    a: [\n      ...aria,\n      // Note: these 3 are used by GFM footnotes, they do work on all links.\n      'dataFootnoteBackref',\n      'dataFootnoteRef',\n      ['className', 'data-footnote-backref'],\n      'href'\n    ],\n    blockquote: ['cite'],\n    // Note: this class is not normally allowed by GH, when manually writing\n    // `code` as HTML in markdown, they adds it some other way.\n    // We can’t do that, so we have to allow it.\n    code: [['className', /^language-./]],\n    del: ['cite'],\n    div: ['itemScope', 'itemType'],\n    dl: [...aria],\n    // Note: this is used by GFM footnotes.\n    h2: [['className', 'sr-only']],\n    img: [...aria, 'longDesc', 'src'],\n    // Note: `input` is not normally allowed by GH, when manually writing\n    // it in markdown, they add it from tasklists some other way.\n    // We can’t do that, so we have to allow it.\n    input: [\n      ['disabled', true],\n      ['type', 'checkbox']\n    ],\n    ins: ['cite'],\n    // Note: this class is not normally allowed by GH, when manually writing\n    // `li` as HTML in markdown, they adds it some other way.\n    // We can’t do that, so we have to allow it.\n    li: [['className', 'task-list-item']],\n    // Note: this class is not normally allowed by GH, when manually writing\n    // `ol` as HTML in markdown, they adds it some other way.\n    // We can’t do that, so we have to allow it.\n    ol: [...aria, ['className', 'contains-task-list']],\n    q: ['cite'],\n    section: ['dataFootnotes', ['className', 'footnotes']],\n    source: ['srcSet'],\n    summary: [...aria],\n    table: [...aria],\n    // Note: this class is not normally allowed by GH, when manually writing\n    // `ol` as HTML in markdown, they adds it some other way.\n    // We can’t do that, so we have to allow it.\n    ul: [...aria, ['className', 'contains-task-list']],\n    '*': [\n      'abbr',\n      'accept',\n      'acceptCharset',\n      'accessKey',\n      'action',\n      'align',\n      'alt',\n      'axis',\n      'border',\n      'cellPadding',\n      'cellSpacing',\n      'char',\n      'charOff',\n      'charSet',\n      'checked',\n      'clear',\n      'colSpan',\n      'color',\n      'cols',\n      'compact',\n      'coords',\n      'dateTime',\n      'dir',\n      // Note: `disabled` is technically allowed on all elements by GH.\n      // But it is useless on everything except `input`.\n      // Because `input`s are normally not allowed, but we allow them for\n      // checkboxes due to tasklists, we allow `disabled` only there.\n      'encType',\n      'frame',\n      'hSpace',\n      'headers',\n      'height',\n      'hrefLang',\n      'htmlFor',\n      'id',\n      'isMap',\n      'itemProp',\n      'label',\n      'lang',\n      'maxLength',\n      'media',\n      'method',\n      'multiple',\n      'name',\n      'noHref',\n      'noShade',\n      'noWrap',\n      'open',\n      'prompt',\n      'readOnly',\n      'rev',\n      'rowSpan',\n      'rows',\n      'rules',\n      'scope',\n      'selected',\n      'shape',\n      'size',\n      'span',\n      'start',\n      'summary',\n      'tabIndex',\n      'title',\n      'useMap',\n      'vAlign',\n      'value',\n      'width'\n    ]\n  },\n  clobber: ['ariaDescribedBy', 'ariaLabelledBy', 'id', 'name'],\n  clobberPrefix: 'user-content-',\n  protocols: {\n    cite: ['http', 'https'],\n    href: ['http', 'https', 'irc', 'ircs', 'mailto', 'xmpp'],\n    longDesc: ['http', 'https'],\n    src: ['http', 'https']\n  },\n  required: {\n    input: {disabled: true, type: 'checkbox'}\n  },\n  strip: ['script'],\n  tagNames: [\n    'a',\n    'b',\n    'blockquote',\n    'br',\n    'code',\n    'dd',\n    'del',\n    'details',\n    'div',\n    'dl',\n    'dt',\n    'em',\n    'h1',\n    'h2',\n    'h3',\n    'h4',\n    'h5',\n    'h6',\n    'hr',\n    'i',\n    'img',\n    // Note: `input` is not normally allowed by GH, when manually writing\n    // it in markdown, they add it from tasklists some other way.\n    // We can’t do that, so we have to allow it.\n    'input',\n    'ins',\n    'kbd',\n    'li',\n    'ol',\n    'p',\n    'picture',\n    'pre',\n    'q',\n    'rp',\n    'rt',\n    'ruby',\n    's',\n    'samp',\n    'section',\n    'source',\n    'span',\n    'strike',\n    'strong',\n    'sub',\n    'summary',\n    'sup',\n    'table',\n    'tbody',\n    'td',\n    'tfoot',\n    'th',\n    'thead',\n    'tr',\n    'tt',\n    'ul',\n    'var'\n  ]\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-sanitize/lib/schema.js\n");

/***/ })

};
;