"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/hast-util-phrasing";
exports.ids = ["vendor-chunks/hast-util-phrasing"];
exports.modules = {

/***/ "(ssr)/./node_modules/hast-util-phrasing/lib/index.js":
/*!******************************************************!*\
  !*** ./node_modules/hast-util-phrasing/lib/index.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   phrasing: () => (/* binding */ phrasing)\n/* harmony export */ });\n/* harmony import */ var hast_util_embedded__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! hast-util-embedded */ \"(ssr)/./node_modules/hast-util-embedded/lib/index.js\");\n/* harmony import */ var hast_util_has_property__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! hast-util-has-property */ \"(ssr)/./node_modules/hast-util-has-property/lib/index.js\");\n/* harmony import */ var hast_util_is_body_ok_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! hast-util-is-body-ok-link */ \"(ssr)/./node_modules/hast-util-is-body-ok-link/lib/index.js\");\n/* harmony import */ var hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! hast-util-is-element */ \"(ssr)/./node_modules/hast-util-is-element/lib/index.js\");\n/**\n * @typedef {import('hast').Nodes} Nodes\n */\n\n\n\n\n\n\nconst basic = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)([\n  'a',\n  'abbr',\n  // `area` is in fact only phrasing if it is inside a `map` element.\n  // However, since `area`s are required to be inside a `map` element, and it’s\n  // a rather involved check, it’s ignored here for now.\n  'area',\n  'b',\n  'bdi',\n  'bdo',\n  'br',\n  'button',\n  'cite',\n  'code',\n  'data',\n  'datalist',\n  'del',\n  'dfn',\n  'em',\n  'i',\n  'input',\n  'ins',\n  'kbd',\n  'keygen',\n  'label',\n  'map',\n  'mark',\n  'meter',\n  'noscript',\n  'output',\n  'progress',\n  'q',\n  'ruby',\n  's',\n  'samp',\n  'script',\n  'select',\n  'small',\n  'span',\n  'strong',\n  'sub',\n  'sup',\n  'template',\n  'textarea',\n  'time',\n  'u',\n  'var',\n  'wbr'\n])\n\nconst meta = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)('meta')\n\n/**\n * Check if the given value is *phrasing* content.\n *\n * @param {Nodes} value\n *   Node to check.\n * @returns {boolean}\n *   Whether `value` is phrasing content.\n */\nfunction phrasing(value) {\n  return Boolean(\n    value.type === 'text' ||\n      basic(value) ||\n      (0,hast_util_embedded__WEBPACK_IMPORTED_MODULE_1__.embedded)(value) ||\n      (0,hast_util_is_body_ok_link__WEBPACK_IMPORTED_MODULE_2__.isBodyOkLink)(value) ||\n      (meta(value) && (0,hast_util_has_property__WEBPACK_IMPORTED_MODULE_3__.hasProperty)(value, 'itemProp'))\n  )\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-phrasing/lib/index.js\n");

/***/ })

};
;