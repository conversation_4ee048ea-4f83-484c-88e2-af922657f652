# Bittensor Development Information

## 🔐 **SECURITY WARNING**
This file contains sensitive development credentials. **DO NOT COMMIT TO VERSION CONTROL**.
Add this file to `.gitignore` immediately.

---

## 📋 **BTDash Test Subnet Information**

### **Subnet Details**
- **Netuid**: `408`
- **Name**: "BTDash Test Subnet"
- **Symbol**: `ඌ` (Sinhala letter)
- **Network**: `test` (testnet)
- **Description**: "Test subnet for BTDash subnet verification feature"
- **GitHub Repository**: https://github.com/btdash/btdash-ecosystem
- **Contact Email**: <EMAIL>
- **Subnet URL**: https://btdash.com
- **Status**: ✅ Active and operational
- **Creation Date**: 2025-01-07

---

## 🔑 **Wallet Information**

### **Primary Wallet (Recovered)**
- **Wallet Name**: `test_subnet_wallet_recovered`
- **Password**: `btdash123`
- **Network**: `test` (testnet)

### **Addresses**
- **Coldkey Address**: `5E6m5CAMQ5p5T12W4bJc8tH7BeuQaMaUKRCJbmmr7z9rfopk`
- **Hotkey Address**: `5HC7jeYt17oMY6QjuMoZgehHcdySh4pQVxNyHFqgxz6jFoTK`
- **Hotkey Name**: `default`

### **Current Balance**
- **Free Balance**: `45.3977 τ` (test TAO)
- **Staked Value**: `0.0000 τ`
- **Total Balance**: `45.3977 τ`

---

## 🔐 **Recovery Mnemonics**

### **⚠️ CRITICAL - STORE SECURELY**
These mnemonics can regenerate the wallet. Keep them safe and offline.

**Coldkey Mnemonic:**
```
lesson tackle kitchen excess present breeze frown toe diet update fun verb
```

**Hotkey Mnemonic:**
```
autumn throw welcome thunder marine wolf palace keen cat nut wife jacket
```

---

## 🛠 **Development Environment**

### **Environment Setup**
- **Location**: Windows WSL environment
- **Path**: `/mnt/c/Users/<USER>/Desktop/G_PROG/Nicolas/btdash-ecosystem`
- **Python Environment**: `bittensor_env` (virtual environment)
- **Activation Command**: `source bittensor_env/bin/activate`

### **Installed Versions**
- **Bittensor**: `9.8.3`
- **Bittensor CLI**: `9.10.0`
- **Network**: `test` (testnet)

---

## 📝 **Common Commands**

### **Wallet Management**
```bash
# Activate environment
source bittensor_env/bin/activate

# List wallets
btcli wallet list

# Check balance
btcli wallet balance --wallet.name test_subnet_wallet_recovered --network test

# Regenerate coldkey (if needed)
btcli wallet regen_coldkey --wallet.name test_subnet_wallet_recovered

# Regenerate hotkey (if needed)
btcli wallet regen_hotkey --wallet.name test_subnet_wallet_recovered --wallet.hotkey default
```

### **Subnet Management**
```bash
# List all subnets
btcli subnets list --network test

# Get subnet info
btcli subnets show --netuid 408 --network test

# Check subnet details
btcli subnet info --netuid 408 --network test
```

---

## 💰 **Financial Information**

### **Subnet Creation Cost**
- **Total Cost**: `4.6023 τ`
- **Original Balance**: `50.0000 τ`
- **Remaining Balance**: `45.3977 τ`
- **Transaction Date**: 2025-01-07

### **Cost Breakdown**
- **Subnet Registration**: `4.6030 τ` (as displayed during creation)
- **Network Fees**: Minimal (included in above)

---

## 🔗 **Integration Information**

### **For BTDash Application**
Use these values in your subnet verification feature:

```javascript
const BITTENSOR_CONFIG = {
  network: 'test',
  netuid: 408,
  subnetName: 'BTDash Test Subnet',
  subnetSymbol: 'ඌ',
  walletAddress: '5E6m5CAMQ5p5T12W4bJc8tH7BeuQaMaUKRCJbmmr7z9rfopk'
};
```

### **API Endpoints**
- **Testnet RPC**: `wss://test.finney.opentensor.ai:443`
- **Testnet Explorer**: https://x.taostats.io/subnets/408

---

## 📚 **Documentation References**

### **Official Documentation**
- **Bittensor Docs**: https://docs.bittensor.com/
- **Subnet Creation Guide**: https://docs.bittensor.com/subnets/create-a-subnet
- **CLI Reference**: https://docs.bittensor.com/btcli

### **Useful Links**
- **Testnet Explorer**: https://x.taostats.io/
- **GitHub Repository**: https://github.com/opentensor/bittensor
- **Discord Community**: https://discord.gg/bittensor

---

## ⚠️ **Important Notes**

1. **Security**: Never share mnemonics or passwords publicly
2. **Testnet Only**: This setup is for testing purposes only
3. **Balance Monitoring**: Keep track of τ balance for continued testing
4. **Backup**: Store mnemonics in multiple secure locations
5. **Environment**: Always activate the virtual environment before using btcli

---

## 🔄 **Troubleshooting**

### **Common Issues**
1. **Password Forgotten**: Use mnemonics to regenerate wallet
2. **Environment Issues**: Ensure WSL and virtual environment are active
3. **Network Issues**: Verify testnet connectivity
4. **Balance Issues**: Check current balance before operations

### **Recovery Process**
If wallet access is lost, use the recovery mnemonics with:
```bash
btcli wallet regen_coldkey --wallet.name [new_wallet_name]
btcli wallet regen_hotkey --wallet.name [new_wallet_name] --wallet.hotkey default
```

---

## 📅 **Maintenance Schedule**

- **Weekly**: Check subnet status and balance
- **Monthly**: Verify subnet is still active
- **As Needed**: Top up balance if running low
- **Before Production**: Migrate to mainnet with proper security measures

---

**Last Updated**: 2025-01-07
**Created By**: Augment Agent
**Purpose**: BTDash subnet verification feature development
