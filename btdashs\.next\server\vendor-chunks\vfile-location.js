"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/vfile-location";
exports.ids = ["vendor-chunks/vfile-location"];
exports.modules = {

/***/ "(ssr)/./node_modules/vfile-location/lib/index.js":
/*!**************************************************!*\
  !*** ./node_modules/vfile-location/lib/index.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   location: () => (/* binding */ location)\n/* harmony export */ });\n/**\n * @import {VFile, Value} from 'vfile'\n * @import {Location} from 'vfile-location'\n */\n\n/**\n * Create an index of the given document to translate between line/column and\n * offset based positional info.\n *\n * Also implemented in Rust in [`wooorm/markdown-rs`][markdown-rs].\n *\n * [markdown-rs]: https://github.com/wooorm/markdown-rs/blob/main/src/util/location.rs\n *\n * @param {VFile | Value} file\n *   File to index.\n * @returns {Location}\n *   Accessors for index.\n */\nfunction location(file) {\n  const value = String(file)\n  /**\n   * List, where each index is a line number (0-based), and each value is the\n   * byte index *after* where the line ends.\n   *\n   * @type {Array<number>}\n   */\n  const indices = []\n\n  return {toOffset, toPoint}\n\n  /** @type {Location['toPoint']} */\n  function toPoint(offset) {\n    if (typeof offset === 'number' && offset > -1 && offset <= value.length) {\n      let index = 0\n\n      while (true) {\n        let end = indices[index]\n\n        if (end === undefined) {\n          const eol = next(value, indices[index - 1])\n          end = eol === -1 ? value.length + 1 : eol + 1\n          indices[index] = end\n        }\n\n        if (end > offset) {\n          return {\n            line: index + 1,\n            column: offset - (index > 0 ? indices[index - 1] : 0) + 1,\n            offset\n          }\n        }\n\n        index++\n      }\n    }\n  }\n\n  /** @type {Location['toOffset']} */\n  function toOffset(point) {\n    if (\n      point &&\n      typeof point.line === 'number' &&\n      typeof point.column === 'number' &&\n      !Number.isNaN(point.line) &&\n      !Number.isNaN(point.column)\n    ) {\n      while (indices.length < point.line) {\n        const from = indices[indices.length - 1]\n        const eol = next(value, from)\n        const end = eol === -1 ? value.length + 1 : eol + 1\n        if (from === end) break\n        indices.push(end)\n      }\n\n      const offset =\n        (point.line > 1 ? indices[point.line - 2] : 0) + point.column - 1\n      // The given `column` could not exist on this line.\n      if (offset < indices[point.line - 1]) return offset\n    }\n  }\n}\n\n/**\n * @param {string} value\n * @param {number} from\n */\nfunction next(value, from) {\n  const cr = value.indexOf('\\r', from)\n  const lf = value.indexOf('\\n', from)\n  if (lf === -1) return cr\n  if (cr === -1 || cr + 1 === lf) return lf\n  return cr < lf ? cr : lf\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/vfile-location/lib/index.js\n");

/***/ })

};
;