"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/hast-util-to-mdast";
exports.ids = ["vendor-chunks/hast-util-to-mdast"];
exports.modules = {

/***/ "(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/a.js":
/*!***********************************************************!*\
  !*** ./node_modules/hast-util-to-mdast/lib/handlers/a.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   a: () => (/* binding */ a)\n/* harmony export */ });\n/**\n * @import {State} from 'hast-util-to-mdast'\n * @import {Element} from 'hast'\n * @import {Link, PhrasingContent} from 'mdast'\n */\n\n/**\n * @param {State} state\n *   State.\n * @param {Readonly<Element>} node\n *   hast element to transform.\n * @returns {Link}\n *   mdast node.\n */\nfunction a(state, node) {\n  const properties = node.properties || {}\n  // Allow potentially “invalid” nodes, they might be unknown.\n  // We also support straddling later.\n  const children = /** @type {Array<PhrasingContent>} */ (state.all(node))\n\n  /** @type {Link} */\n  const result = {\n    type: 'link',\n    url: state.resolve(String(properties.href || '') || null),\n    title: properties.title ? String(properties.title) : null,\n    children\n  }\n  state.patch(node, result)\n  return result\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L2xpYi9oYW5kbGVycy9hLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBLFlBQVksT0FBTztBQUNuQixZQUFZLFNBQVM7QUFDckIsWUFBWSx1QkFBdUI7QUFDbkM7O0FBRUE7QUFDQSxXQUFXLE9BQU87QUFDbEI7QUFDQSxXQUFXLG1CQUFtQjtBQUM5QjtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQSw4QkFBOEIsd0JBQXdCOztBQUV0RCxhQUFhLE1BQU07QUFDbkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGJlbmppXFxEZXNrdG9wXFxHX1BST0dcXE5pY29sYXNcXGJ0ZGFzaC1lY29zeXN0ZW1cXGJ0ZGFzaHNcXG5vZGVfbW9kdWxlc1xcaGFzdC11dGlsLXRvLW1kYXN0XFxsaWJcXGhhbmRsZXJzXFxhLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGltcG9ydCB7U3RhdGV9IGZyb20gJ2hhc3QtdXRpbC10by1tZGFzdCdcbiAqIEBpbXBvcnQge0VsZW1lbnR9IGZyb20gJ2hhc3QnXG4gKiBAaW1wb3J0IHtMaW5rLCBQaHJhc2luZ0NvbnRlbnR9IGZyb20gJ21kYXN0J1xuICovXG5cbi8qKlxuICogQHBhcmFtIHtTdGF0ZX0gc3RhdGVcbiAqICAgU3RhdGUuXG4gKiBAcGFyYW0ge1JlYWRvbmx5PEVsZW1lbnQ+fSBub2RlXG4gKiAgIGhhc3QgZWxlbWVudCB0byB0cmFuc2Zvcm0uXG4gKiBAcmV0dXJucyB7TGlua31cbiAqICAgbWRhc3Qgbm9kZS5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGEoc3RhdGUsIG5vZGUpIHtcbiAgY29uc3QgcHJvcGVydGllcyA9IG5vZGUucHJvcGVydGllcyB8fCB7fVxuICAvLyBBbGxvdyBwb3RlbnRpYWxseSDigJxpbnZhbGlk4oCdIG5vZGVzLCB0aGV5IG1pZ2h0IGJlIHVua25vd24uXG4gIC8vIFdlIGFsc28gc3VwcG9ydCBzdHJhZGRsaW5nIGxhdGVyLlxuICBjb25zdCBjaGlsZHJlbiA9IC8qKiBAdHlwZSB7QXJyYXk8UGhyYXNpbmdDb250ZW50Pn0gKi8gKHN0YXRlLmFsbChub2RlKSlcblxuICAvKiogQHR5cGUge0xpbmt9ICovXG4gIGNvbnN0IHJlc3VsdCA9IHtcbiAgICB0eXBlOiAnbGluaycsXG4gICAgdXJsOiBzdGF0ZS5yZXNvbHZlKFN0cmluZyhwcm9wZXJ0aWVzLmhyZWYgfHwgJycpIHx8IG51bGwpLFxuICAgIHRpdGxlOiBwcm9wZXJ0aWVzLnRpdGxlID8gU3RyaW5nKHByb3BlcnRpZXMudGl0bGUpIDogbnVsbCxcbiAgICBjaGlsZHJlblxuICB9XG4gIHN0YXRlLnBhdGNoKG5vZGUsIHJlc3VsdClcbiAgcmV0dXJuIHJlc3VsdFxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/a.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/base.js":
/*!**************************************************************!*\
  !*** ./node_modules/hast-util-to-mdast/lib/handlers/base.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   base: () => (/* binding */ base)\n/* harmony export */ });\n/**\n * @import {State} from 'hast-util-to-mdast'\n * @import {Element} from 'hast'\n */\n\n/**\n * @param {State} state\n *   State.\n * @param {Readonly<Element>} node\n *   hast element to transform.\n * @returns {undefined}\n *   Nothing.\n */\nfunction base(state, node) {\n  if (!state.baseFound) {\n    state.frozenBaseUrl =\n      String((node.properties && node.properties.href) || '') || undefined\n    state.baseFound = true\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L2xpYi9oYW5kbGVycy9iYXNlLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBLFlBQVksT0FBTztBQUNuQixZQUFZLFNBQVM7QUFDckI7O0FBRUE7QUFDQSxXQUFXLE9BQU87QUFDbEI7QUFDQSxXQUFXLG1CQUFtQjtBQUM5QjtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYmVuamlcXERlc2t0b3BcXEdfUFJPR1xcTmljb2xhc1xcYnRkYXNoLWVjb3N5c3RlbVxcYnRkYXNoc1xcbm9kZV9tb2R1bGVzXFxoYXN0LXV0aWwtdG8tbWRhc3RcXGxpYlxcaGFuZGxlcnNcXGJhc2UuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAaW1wb3J0IHtTdGF0ZX0gZnJvbSAnaGFzdC11dGlsLXRvLW1kYXN0J1xuICogQGltcG9ydCB7RWxlbWVudH0gZnJvbSAnaGFzdCdcbiAqL1xuXG4vKipcbiAqIEBwYXJhbSB7U3RhdGV9IHN0YXRlXG4gKiAgIFN0YXRlLlxuICogQHBhcmFtIHtSZWFkb25seTxFbGVtZW50Pn0gbm9kZVxuICogICBoYXN0IGVsZW1lbnQgdG8gdHJhbnNmb3JtLlxuICogQHJldHVybnMge3VuZGVmaW5lZH1cbiAqICAgTm90aGluZy5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGJhc2Uoc3RhdGUsIG5vZGUpIHtcbiAgaWYgKCFzdGF0ZS5iYXNlRm91bmQpIHtcbiAgICBzdGF0ZS5mcm96ZW5CYXNlVXJsID1cbiAgICAgIFN0cmluZygobm9kZS5wcm9wZXJ0aWVzICYmIG5vZGUucHJvcGVydGllcy5ocmVmKSB8fCAnJykgfHwgdW5kZWZpbmVkXG4gICAgc3RhdGUuYmFzZUZvdW5kID0gdHJ1ZVxuICB9XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/base.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/blockquote.js":
/*!********************************************************************!*\
  !*** ./node_modules/hast-util-to-mdast/lib/handlers/blockquote.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   blockquote: () => (/* binding */ blockquote)\n/* harmony export */ });\n/**\n * @import {State} from 'hast-util-to-mdast'\n * @import {Element} from 'hast'\n * @import {Blockquote} from 'mdast'\n */\n\n/**\n * @param {State} state\n *   State.\n * @param {Readonly<Element>} node\n *   hast element to transform.\n * @returns {Blockquote}\n *   mdast node.\n */\nfunction blockquote(state, node) {\n  /** @type {Blockquote} */\n  const result = {type: 'blockquote', children: state.toFlow(state.all(node))}\n  state.patch(node, result)\n  return result\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L2xpYi9oYW5kbGVycy9ibG9ja3F1b3RlLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBLFlBQVksT0FBTztBQUNuQixZQUFZLFNBQVM7QUFDckIsWUFBWSxZQUFZO0FBQ3hCOztBQUVBO0FBQ0EsV0FBVyxPQUFPO0FBQ2xCO0FBQ0EsV0FBVyxtQkFBbUI7QUFDOUI7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNPO0FBQ1AsYUFBYSxZQUFZO0FBQ3pCLGtCQUFrQjtBQUNsQjtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYmVuamlcXERlc2t0b3BcXEdfUFJPR1xcTmljb2xhc1xcYnRkYXNoLWVjb3N5c3RlbVxcYnRkYXNoc1xcbm9kZV9tb2R1bGVzXFxoYXN0LXV0aWwtdG8tbWRhc3RcXGxpYlxcaGFuZGxlcnNcXGJsb2NrcXVvdGUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAaW1wb3J0IHtTdGF0ZX0gZnJvbSAnaGFzdC11dGlsLXRvLW1kYXN0J1xuICogQGltcG9ydCB7RWxlbWVudH0gZnJvbSAnaGFzdCdcbiAqIEBpbXBvcnQge0Jsb2NrcXVvdGV9IGZyb20gJ21kYXN0J1xuICovXG5cbi8qKlxuICogQHBhcmFtIHtTdGF0ZX0gc3RhdGVcbiAqICAgU3RhdGUuXG4gKiBAcGFyYW0ge1JlYWRvbmx5PEVsZW1lbnQ+fSBub2RlXG4gKiAgIGhhc3QgZWxlbWVudCB0byB0cmFuc2Zvcm0uXG4gKiBAcmV0dXJucyB7QmxvY2txdW90ZX1cbiAqICAgbWRhc3Qgbm9kZS5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGJsb2NrcXVvdGUoc3RhdGUsIG5vZGUpIHtcbiAgLyoqIEB0eXBlIHtCbG9ja3F1b3RlfSAqL1xuICBjb25zdCByZXN1bHQgPSB7dHlwZTogJ2Jsb2NrcXVvdGUnLCBjaGlsZHJlbjogc3RhdGUudG9GbG93KHN0YXRlLmFsbChub2RlKSl9XG4gIHN0YXRlLnBhdGNoKG5vZGUsIHJlc3VsdClcbiAgcmV0dXJuIHJlc3VsdFxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/blockquote.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/br.js":
/*!************************************************************!*\
  !*** ./node_modules/hast-util-to-mdast/lib/handlers/br.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   br: () => (/* binding */ br)\n/* harmony export */ });\n/**\n * @import {State} from 'hast-util-to-mdast'\n * @import {Element} from 'hast'\n * @import {Break} from 'mdast'\n */\n\n/**\n * @param {State} state\n *   State.\n * @param {Readonly<Element>} node\n *   hast element to transform.\n * @returns {Break}\n *   mdast node.\n */\nfunction br(state, node) {\n  /** @type {Break} */\n  const result = {type: 'break'}\n  state.patch(node, result)\n  return result\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L2xpYi9oYW5kbGVycy9ici5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQSxZQUFZLE9BQU87QUFDbkIsWUFBWSxTQUFTO0FBQ3JCLFlBQVksT0FBTztBQUNuQjs7QUFFQTtBQUNBLFdBQVcsT0FBTztBQUNsQjtBQUNBLFdBQVcsbUJBQW1CO0FBQzlCO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDTztBQUNQLGFBQWEsT0FBTztBQUNwQixrQkFBa0I7QUFDbEI7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGJlbmppXFxEZXNrdG9wXFxHX1BST0dcXE5pY29sYXNcXGJ0ZGFzaC1lY29zeXN0ZW1cXGJ0ZGFzaHNcXG5vZGVfbW9kdWxlc1xcaGFzdC11dGlsLXRvLW1kYXN0XFxsaWJcXGhhbmRsZXJzXFxici5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBpbXBvcnQge1N0YXRlfSBmcm9tICdoYXN0LXV0aWwtdG8tbWRhc3QnXG4gKiBAaW1wb3J0IHtFbGVtZW50fSBmcm9tICdoYXN0J1xuICogQGltcG9ydCB7QnJlYWt9IGZyb20gJ21kYXN0J1xuICovXG5cbi8qKlxuICogQHBhcmFtIHtTdGF0ZX0gc3RhdGVcbiAqICAgU3RhdGUuXG4gKiBAcGFyYW0ge1JlYWRvbmx5PEVsZW1lbnQ+fSBub2RlXG4gKiAgIGhhc3QgZWxlbWVudCB0byB0cmFuc2Zvcm0uXG4gKiBAcmV0dXJucyB7QnJlYWt9XG4gKiAgIG1kYXN0IG5vZGUuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBicihzdGF0ZSwgbm9kZSkge1xuICAvKiogQHR5cGUge0JyZWFrfSAqL1xuICBjb25zdCByZXN1bHQgPSB7dHlwZTogJ2JyZWFrJ31cbiAgc3RhdGUucGF0Y2gobm9kZSwgcmVzdWx0KVxuICByZXR1cm4gcmVzdWx0XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/br.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/code.js":
/*!**************************************************************!*\
  !*** ./node_modules/hast-util-to-mdast/lib/handlers/code.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   code: () => (/* binding */ code)\n/* harmony export */ });\n/* harmony import */ var hast_util_to_text__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! hast-util-to-text */ \"(ssr)/./node_modules/hast-util-to-text/lib/index.js\");\n/* harmony import */ var trim_trailing_lines__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! trim-trailing-lines */ \"(ssr)/./node_modules/trim-trailing-lines/index.js\");\n/**\n * @import {State} from 'hast-util-to-mdast'\n * @import {Element} from 'hast'\n * @import {Code} from 'mdast'\n */\n\n\n\n\nconst prefix = 'language-'\n\n/**\n * @param {State} state\n *   State.\n * @param {Readonly<Element>} node\n *   hast element to transform.\n * @returns {Code}\n *   mdast node.\n */\nfunction code(state, node) {\n  const children = node.children\n  let index = -1\n  /** @type {Array<number | string> | undefined} */\n  let classList\n  /** @type {string | undefined} */\n  let lang\n\n  if (node.tagName === 'pre') {\n    while (++index < children.length) {\n      const child = children[index]\n\n      if (\n        child.type === 'element' &&\n        child.tagName === 'code' &&\n        child.properties &&\n        child.properties.className &&\n        Array.isArray(child.properties.className)\n      ) {\n        classList = child.properties.className\n        break\n      }\n    }\n  }\n\n  if (classList) {\n    index = -1\n\n    while (++index < classList.length) {\n      if (String(classList[index]).slice(0, prefix.length) === prefix) {\n        lang = String(classList[index]).slice(prefix.length)\n        break\n      }\n    }\n  }\n\n  /** @type {Code} */\n  const result = {\n    type: 'code',\n    lang: lang || null,\n    meta: null,\n    value: (0,trim_trailing_lines__WEBPACK_IMPORTED_MODULE_0__.trimTrailingLines)((0,hast_util_to_text__WEBPACK_IMPORTED_MODULE_1__.toText)(node))\n  }\n  state.patch(node, result)\n  return result\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/code.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/comment.js":
/*!*****************************************************************!*\
  !*** ./node_modules/hast-util-to-mdast/lib/handlers/comment.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   comment: () => (/* binding */ comment)\n/* harmony export */ });\n/**\n * @import {State} from 'hast-util-to-mdast'\n * @import {Comment} from 'hast'\n * @import {Html} from 'mdast'\n */\n\n/**\n * @param {State} state\n *   State.\n * @param {Readonly<Comment>} node\n *   hast element to transform.\n * @returns {Html}\n *   mdast node.\n */\nfunction comment(state, node) {\n  /** @type {Html} */\n  const result = {\n    type: 'html',\n    value: '<!--' + node.value + '-->'\n  }\n  state.patch(node, result)\n  return result\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L2xpYi9oYW5kbGVycy9jb21tZW50LmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBLFlBQVksT0FBTztBQUNuQixZQUFZLFNBQVM7QUFDckIsWUFBWSxNQUFNO0FBQ2xCOztBQUVBO0FBQ0EsV0FBVyxPQUFPO0FBQ2xCO0FBQ0EsV0FBVyxtQkFBbUI7QUFDOUI7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNPO0FBQ1AsYUFBYSxNQUFNO0FBQ25CO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGJlbmppXFxEZXNrdG9wXFxHX1BST0dcXE5pY29sYXNcXGJ0ZGFzaC1lY29zeXN0ZW1cXGJ0ZGFzaHNcXG5vZGVfbW9kdWxlc1xcaGFzdC11dGlsLXRvLW1kYXN0XFxsaWJcXGhhbmRsZXJzXFxjb21tZW50LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGltcG9ydCB7U3RhdGV9IGZyb20gJ2hhc3QtdXRpbC10by1tZGFzdCdcbiAqIEBpbXBvcnQge0NvbW1lbnR9IGZyb20gJ2hhc3QnXG4gKiBAaW1wb3J0IHtIdG1sfSBmcm9tICdtZGFzdCdcbiAqL1xuXG4vKipcbiAqIEBwYXJhbSB7U3RhdGV9IHN0YXRlXG4gKiAgIFN0YXRlLlxuICogQHBhcmFtIHtSZWFkb25seTxDb21tZW50Pn0gbm9kZVxuICogICBoYXN0IGVsZW1lbnQgdG8gdHJhbnNmb3JtLlxuICogQHJldHVybnMge0h0bWx9XG4gKiAgIG1kYXN0IG5vZGUuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBjb21tZW50KHN0YXRlLCBub2RlKSB7XG4gIC8qKiBAdHlwZSB7SHRtbH0gKi9cbiAgY29uc3QgcmVzdWx0ID0ge1xuICAgIHR5cGU6ICdodG1sJyxcbiAgICB2YWx1ZTogJzwhLS0nICsgbm9kZS52YWx1ZSArICctLT4nXG4gIH1cbiAgc3RhdGUucGF0Y2gobm9kZSwgcmVzdWx0KVxuICByZXR1cm4gcmVzdWx0XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/comment.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/del.js":
/*!*************************************************************!*\
  !*** ./node_modules/hast-util-to-mdast/lib/handlers/del.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   del: () => (/* binding */ del)\n/* harmony export */ });\n/**\n * @import {State} from 'hast-util-to-mdast'\n * @import {Element} from 'hast'\n * @import {Delete, PhrasingContent} from 'mdast'\n */\n\n/**\n * @param {State} state\n *   State.\n * @param {Readonly<Element>} node\n *   hast element to transform.\n * @returns {Delete}\n *   mdast node.\n */\nfunction del(state, node) {\n  // Allow potentially “invalid” nodes, they might be unknown.\n  // We also support straddling later.\n  const children = /** @type {Array<PhrasingContent>} */ (state.all(node))\n  /** @type {Delete} */\n  const result = {type: 'delete', children}\n  state.patch(node, result)\n  return result\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L2xpYi9oYW5kbGVycy9kZWwuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0EsWUFBWSxPQUFPO0FBQ25CLFlBQVksU0FBUztBQUNyQixZQUFZLHlCQUF5QjtBQUNyQzs7QUFFQTtBQUNBLFdBQVcsT0FBTztBQUNsQjtBQUNBLFdBQVcsbUJBQW1CO0FBQzlCO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQSw4QkFBOEIsd0JBQXdCO0FBQ3RELGFBQWEsUUFBUTtBQUNyQixrQkFBa0I7QUFDbEI7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGJlbmppXFxEZXNrdG9wXFxHX1BST0dcXE5pY29sYXNcXGJ0ZGFzaC1lY29zeXN0ZW1cXGJ0ZGFzaHNcXG5vZGVfbW9kdWxlc1xcaGFzdC11dGlsLXRvLW1kYXN0XFxsaWJcXGhhbmRsZXJzXFxkZWwuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAaW1wb3J0IHtTdGF0ZX0gZnJvbSAnaGFzdC11dGlsLXRvLW1kYXN0J1xuICogQGltcG9ydCB7RWxlbWVudH0gZnJvbSAnaGFzdCdcbiAqIEBpbXBvcnQge0RlbGV0ZSwgUGhyYXNpbmdDb250ZW50fSBmcm9tICdtZGFzdCdcbiAqL1xuXG4vKipcbiAqIEBwYXJhbSB7U3RhdGV9IHN0YXRlXG4gKiAgIFN0YXRlLlxuICogQHBhcmFtIHtSZWFkb25seTxFbGVtZW50Pn0gbm9kZVxuICogICBoYXN0IGVsZW1lbnQgdG8gdHJhbnNmb3JtLlxuICogQHJldHVybnMge0RlbGV0ZX1cbiAqICAgbWRhc3Qgbm9kZS5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGRlbChzdGF0ZSwgbm9kZSkge1xuICAvLyBBbGxvdyBwb3RlbnRpYWxseSDigJxpbnZhbGlk4oCdIG5vZGVzLCB0aGV5IG1pZ2h0IGJlIHVua25vd24uXG4gIC8vIFdlIGFsc28gc3VwcG9ydCBzdHJhZGRsaW5nIGxhdGVyLlxuICBjb25zdCBjaGlsZHJlbiA9IC8qKiBAdHlwZSB7QXJyYXk8UGhyYXNpbmdDb250ZW50Pn0gKi8gKHN0YXRlLmFsbChub2RlKSlcbiAgLyoqIEB0eXBlIHtEZWxldGV9ICovXG4gIGNvbnN0IHJlc3VsdCA9IHt0eXBlOiAnZGVsZXRlJywgY2hpbGRyZW59XG4gIHN0YXRlLnBhdGNoKG5vZGUsIHJlc3VsdClcbiAgcmV0dXJuIHJlc3VsdFxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/del.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/dl.js":
/*!************************************************************!*\
  !*** ./node_modules/hast-util-to-mdast/lib/handlers/dl.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   dl: () => (/* binding */ dl)\n/* harmony export */ });\n/* harmony import */ var _util_list_items_spread_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/list-items-spread.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/util/list-items-spread.js\");\n/**\n * @import {State} from 'hast-util-to-mdast'\n * @import {ElementContent, Element} from 'hast'\n * @import {BlockContent, DefinitionContent, ListContent, ListItem, List} from 'mdast'\n */\n\n/**\n * @typedef Group\n *   Title/definition group.\n * @property {Array<Element>} titles\n *   One or more titles.\n * @property {Array<ElementContent>} definitions\n *   One or more definitions.\n */\n\n\n\n/**\n * @param {State} state\n *   State.\n * @param {Readonly<Element>} node\n *   hast element to transform.\n * @returns {List | undefined}\n *   mdast node.\n */\nfunction dl(state, node) {\n  /** @type {Array<ElementContent>} */\n  const clean = []\n  /** @type {Array<Group>} */\n  const groups = []\n  let index = -1\n\n  // Unwrap `<div>`s\n  while (++index < node.children.length) {\n    const child = node.children[index]\n\n    if (child.type === 'element' && child.tagName === 'div') {\n      clean.push(...child.children)\n    } else {\n      clean.push(child)\n    }\n  }\n\n  /** @type {Group} */\n  let group = {definitions: [], titles: []}\n  index = -1\n\n  // Group titles and definitions.\n  while (++index < clean.length) {\n    const child = clean[index]\n\n    if (child.type === 'element' && child.tagName === 'dt') {\n      const previous = clean[index - 1]\n\n      if (\n        previous &&\n        previous.type === 'element' &&\n        previous.tagName === 'dd'\n      ) {\n        groups.push(group)\n        group = {definitions: [], titles: []}\n      }\n\n      group.titles.push(child)\n    } else {\n      group.definitions.push(child)\n    }\n  }\n\n  groups.push(group)\n\n  // Create items.\n  index = -1\n  /** @type {Array<ListContent>} */\n  const content = []\n\n  while (++index < groups.length) {\n    const result = [\n      ...handle(state, groups[index].titles),\n      ...handle(state, groups[index].definitions)\n    ]\n\n    if (result.length > 0) {\n      content.push({\n        type: 'listItem',\n        spread: result.length > 1,\n        checked: null,\n        children: result\n      })\n    }\n  }\n\n  // Create a list if there are items.\n  if (content.length > 0) {\n    /** @type {List} */\n    const result = {\n      type: 'list',\n      ordered: false,\n      start: null,\n      spread: (0,_util_list_items_spread_js__WEBPACK_IMPORTED_MODULE_0__.listItemsSpread)(content),\n      children: content\n    }\n    state.patch(node, result)\n    return result\n  }\n}\n\n/**\n * @param {State} state\n *   State.\n * @param {Array<ElementContent>} children\n *   hast element children to transform.\n * @returns {Array<BlockContent | DefinitionContent>}\n *   mdast nodes.\n */\nfunction handle(state, children) {\n  const nodes = state.all({type: 'root', children})\n  const listItems = state.toSpecificContent(nodes, create)\n\n  if (listItems.length === 0) {\n    return []\n  }\n\n  if (listItems.length === 1) {\n    return listItems[0].children\n  }\n\n  return [\n    {\n      type: 'list',\n      ordered: false,\n      start: null,\n      spread: (0,_util_list_items_spread_js__WEBPACK_IMPORTED_MODULE_0__.listItemsSpread)(listItems),\n      children: listItems\n    }\n  ]\n}\n\n/**\n * @returns {ListItem}\n */\nfunction create() {\n  return {type: 'listItem', spread: false, checked: null, children: []}\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/dl.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/em.js":
/*!************************************************************!*\
  !*** ./node_modules/hast-util-to-mdast/lib/handlers/em.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   em: () => (/* binding */ em)\n/* harmony export */ });\n/**\n * @import {State} from 'hast-util-to-mdast'\n * @import {Element} from 'hast'\n * @import {Emphasis, PhrasingContent} from 'mdast'\n */\n\n/**\n * @param {State} state\n *   State.\n * @param {Readonly<Element>} node\n *   hast element to transform.\n * @returns {Emphasis}\n *   mdast node.\n */\nfunction em(state, node) {\n  // Allow potentially “invalid” nodes, they might be unknown.\n  // We also support straddling later.\n  const children = /** @type {Array<PhrasingContent>} */ (state.all(node))\n\n  /** @type {Emphasis} */\n  const result = {type: 'emphasis', children}\n  state.patch(node, result)\n  return result\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L2xpYi9oYW5kbGVycy9lbS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQSxZQUFZLE9BQU87QUFDbkIsWUFBWSxTQUFTO0FBQ3JCLFlBQVksMkJBQTJCO0FBQ3ZDOztBQUVBO0FBQ0EsV0FBVyxPQUFPO0FBQ2xCO0FBQ0EsV0FBVyxtQkFBbUI7QUFDOUI7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBLDhCQUE4Qix3QkFBd0I7O0FBRXRELGFBQWEsVUFBVTtBQUN2QixrQkFBa0I7QUFDbEI7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGJlbmppXFxEZXNrdG9wXFxHX1BST0dcXE5pY29sYXNcXGJ0ZGFzaC1lY29zeXN0ZW1cXGJ0ZGFzaHNcXG5vZGVfbW9kdWxlc1xcaGFzdC11dGlsLXRvLW1kYXN0XFxsaWJcXGhhbmRsZXJzXFxlbS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBpbXBvcnQge1N0YXRlfSBmcm9tICdoYXN0LXV0aWwtdG8tbWRhc3QnXG4gKiBAaW1wb3J0IHtFbGVtZW50fSBmcm9tICdoYXN0J1xuICogQGltcG9ydCB7RW1waGFzaXMsIFBocmFzaW5nQ29udGVudH0gZnJvbSAnbWRhc3QnXG4gKi9cblxuLyoqXG4gKiBAcGFyYW0ge1N0YXRlfSBzdGF0ZVxuICogICBTdGF0ZS5cbiAqIEBwYXJhbSB7UmVhZG9ubHk8RWxlbWVudD59IG5vZGVcbiAqICAgaGFzdCBlbGVtZW50IHRvIHRyYW5zZm9ybS5cbiAqIEByZXR1cm5zIHtFbXBoYXNpc31cbiAqICAgbWRhc3Qgbm9kZS5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGVtKHN0YXRlLCBub2RlKSB7XG4gIC8vIEFsbG93IHBvdGVudGlhbGx5IOKAnGludmFsaWTigJ0gbm9kZXMsIHRoZXkgbWlnaHQgYmUgdW5rbm93bi5cbiAgLy8gV2UgYWxzbyBzdXBwb3J0IHN0cmFkZGxpbmcgbGF0ZXIuXG4gIGNvbnN0IGNoaWxkcmVuID0gLyoqIEB0eXBlIHtBcnJheTxQaHJhc2luZ0NvbnRlbnQ+fSAqLyAoc3RhdGUuYWxsKG5vZGUpKVxuXG4gIC8qKiBAdHlwZSB7RW1waGFzaXN9ICovXG4gIGNvbnN0IHJlc3VsdCA9IHt0eXBlOiAnZW1waGFzaXMnLCBjaGlsZHJlbn1cbiAgc3RhdGUucGF0Y2gobm9kZSwgcmVzdWx0KVxuICByZXR1cm4gcmVzdWx0XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/em.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/heading.js":
/*!*****************************************************************!*\
  !*** ./node_modules/hast-util-to-mdast/lib/handlers/heading.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   heading: () => (/* binding */ heading)\n/* harmony export */ });\n/* harmony import */ var _util_drop_surrounding_breaks_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/drop-surrounding-breaks.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/util/drop-surrounding-breaks.js\");\n/**\n * @import {State} from 'hast-util-to-mdast'\n * @import {Element} from 'hast'\n * @import {Heading, PhrasingContent} from 'mdast'\n */\n\n\n\n/**\n * @param {State} state\n *   State.\n * @param {Readonly<Element>} node\n *   hast element to transform.\n * @returns {Heading}\n *   mdast node.\n */\nfunction heading(state, node) {\n  const depth = /** @type {Heading['depth']} */ (\n    /* c8 ignore next */\n    Number(node.tagName.charAt(1)) || 1\n  )\n  const children = (0,_util_drop_surrounding_breaks_js__WEBPACK_IMPORTED_MODULE_0__.dropSurroundingBreaks)(\n    /** @type {Array<PhrasingContent>} */ (state.all(node))\n  )\n\n  /** @type {Heading} */\n  const result = {type: 'heading', depth, children}\n  state.patch(node, result)\n  return result\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L2xpYi9oYW5kbGVycy9oZWFkaW5nLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDQSxZQUFZLE9BQU87QUFDbkIsWUFBWSxTQUFTO0FBQ3JCLFlBQVksMEJBQTBCO0FBQ3RDOztBQUV3RTs7QUFFeEU7QUFDQSxXQUFXLE9BQU87QUFDbEI7QUFDQSxXQUFXLG1CQUFtQjtBQUM5QjtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ087QUFDUCwyQkFBMkIsa0JBQWtCO0FBQzdDO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQix1RkFBcUI7QUFDeEMsZUFBZSx3QkFBd0I7QUFDdkM7O0FBRUEsYUFBYSxTQUFTO0FBQ3RCLGtCQUFrQjtBQUNsQjtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYmVuamlcXERlc2t0b3BcXEdfUFJPR1xcTmljb2xhc1xcYnRkYXNoLWVjb3N5c3RlbVxcYnRkYXNoc1xcbm9kZV9tb2R1bGVzXFxoYXN0LXV0aWwtdG8tbWRhc3RcXGxpYlxcaGFuZGxlcnNcXGhlYWRpbmcuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAaW1wb3J0IHtTdGF0ZX0gZnJvbSAnaGFzdC11dGlsLXRvLW1kYXN0J1xuICogQGltcG9ydCB7RWxlbWVudH0gZnJvbSAnaGFzdCdcbiAqIEBpbXBvcnQge0hlYWRpbmcsIFBocmFzaW5nQ29udGVudH0gZnJvbSAnbWRhc3QnXG4gKi9cblxuaW1wb3J0IHtkcm9wU3Vycm91bmRpbmdCcmVha3N9IGZyb20gJy4uL3V0aWwvZHJvcC1zdXJyb3VuZGluZy1icmVha3MuanMnXG5cbi8qKlxuICogQHBhcmFtIHtTdGF0ZX0gc3RhdGVcbiAqICAgU3RhdGUuXG4gKiBAcGFyYW0ge1JlYWRvbmx5PEVsZW1lbnQ+fSBub2RlXG4gKiAgIGhhc3QgZWxlbWVudCB0byB0cmFuc2Zvcm0uXG4gKiBAcmV0dXJucyB7SGVhZGluZ31cbiAqICAgbWRhc3Qgbm9kZS5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGhlYWRpbmcoc3RhdGUsIG5vZGUpIHtcbiAgY29uc3QgZGVwdGggPSAvKiogQHR5cGUge0hlYWRpbmdbJ2RlcHRoJ119ICovIChcbiAgICAvKiBjOCBpZ25vcmUgbmV4dCAqL1xuICAgIE51bWJlcihub2RlLnRhZ05hbWUuY2hhckF0KDEpKSB8fCAxXG4gIClcbiAgY29uc3QgY2hpbGRyZW4gPSBkcm9wU3Vycm91bmRpbmdCcmVha3MoXG4gICAgLyoqIEB0eXBlIHtBcnJheTxQaHJhc2luZ0NvbnRlbnQ+fSAqLyAoc3RhdGUuYWxsKG5vZGUpKVxuICApXG5cbiAgLyoqIEB0eXBlIHtIZWFkaW5nfSAqL1xuICBjb25zdCByZXN1bHQgPSB7dHlwZTogJ2hlYWRpbmcnLCBkZXB0aCwgY2hpbGRyZW59XG4gIHN0YXRlLnBhdGNoKG5vZGUsIHJlc3VsdClcbiAgcmV0dXJuIHJlc3VsdFxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/heading.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/hr.js":
/*!************************************************************!*\
  !*** ./node_modules/hast-util-to-mdast/lib/handlers/hr.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   hr: () => (/* binding */ hr)\n/* harmony export */ });\n/**\n * @import {State} from 'hast-util-to-mdast'\n * @import {Element} from 'hast'\n * @import {ThematicBreak} from 'mdast'\n */\n\n/**\n * @param {State} state\n *   State.\n * @param {Readonly<Element>} node\n *   hast element to transform.\n * @returns {ThematicBreak}\n *   mdast node.\n */\nfunction hr(state, node) {\n  /** @type {ThematicBreak} */\n  const result = {type: 'thematicBreak'}\n  state.patch(node, result)\n  return result\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L2xpYi9oYW5kbGVycy9oci5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQSxZQUFZLE9BQU87QUFDbkIsWUFBWSxTQUFTO0FBQ3JCLFlBQVksZUFBZTtBQUMzQjs7QUFFQTtBQUNBLFdBQVcsT0FBTztBQUNsQjtBQUNBLFdBQVcsbUJBQW1CO0FBQzlCO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDTztBQUNQLGFBQWEsZUFBZTtBQUM1QixrQkFBa0I7QUFDbEI7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGJlbmppXFxEZXNrdG9wXFxHX1BST0dcXE5pY29sYXNcXGJ0ZGFzaC1lY29zeXN0ZW1cXGJ0ZGFzaHNcXG5vZGVfbW9kdWxlc1xcaGFzdC11dGlsLXRvLW1kYXN0XFxsaWJcXGhhbmRsZXJzXFxoci5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBpbXBvcnQge1N0YXRlfSBmcm9tICdoYXN0LXV0aWwtdG8tbWRhc3QnXG4gKiBAaW1wb3J0IHtFbGVtZW50fSBmcm9tICdoYXN0J1xuICogQGltcG9ydCB7VGhlbWF0aWNCcmVha30gZnJvbSAnbWRhc3QnXG4gKi9cblxuLyoqXG4gKiBAcGFyYW0ge1N0YXRlfSBzdGF0ZVxuICogICBTdGF0ZS5cbiAqIEBwYXJhbSB7UmVhZG9ubHk8RWxlbWVudD59IG5vZGVcbiAqICAgaGFzdCBlbGVtZW50IHRvIHRyYW5zZm9ybS5cbiAqIEByZXR1cm5zIHtUaGVtYXRpY0JyZWFrfVxuICogICBtZGFzdCBub2RlLlxuICovXG5leHBvcnQgZnVuY3Rpb24gaHIoc3RhdGUsIG5vZGUpIHtcbiAgLyoqIEB0eXBlIHtUaGVtYXRpY0JyZWFrfSAqL1xuICBjb25zdCByZXN1bHQgPSB7dHlwZTogJ3RoZW1hdGljQnJlYWsnfVxuICBzdGF0ZS5wYXRjaChub2RlLCByZXN1bHQpXG4gIHJldHVybiByZXN1bHRcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/hr.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/iframe.js":
/*!****************************************************************!*\
  !*** ./node_modules/hast-util-to-mdast/lib/handlers/iframe.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   iframe: () => (/* binding */ iframe)\n/* harmony export */ });\n/**\n * @import {State} from 'hast-util-to-mdast'\n * @import {Element} from 'hast'\n * @import {Link} from 'mdast'\n */\n\n/**\n * @param {State} state\n *   State.\n * @param {Readonly<Element>} node\n *   hast element to transform.\n * @returns {Link | undefined}\n *   mdast node.\n */\nfunction iframe(state, node) {\n  const properties = node.properties || {}\n  const source = String(properties.src || '')\n  const title = String(properties.title || '')\n\n  // Only create a link if there is a title.\n  // We can’t use the content of the frame because conforming HTML parsers treat\n  // it as text, whereas legacy parsers treat it as HTML, so it will likely\n  // contain tags that will show up in text.\n  if (source && title) {\n    /** @type {Link} */\n    const result = {\n      type: 'link',\n      title: null,\n      url: state.resolve(source),\n      children: [{type: 'text', value: title}]\n    }\n    state.patch(node, result)\n    return result\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/iframe.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/img.js":
/*!*************************************************************!*\
  !*** ./node_modules/hast-util-to-mdast/lib/handlers/img.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   img: () => (/* binding */ img)\n/* harmony export */ });\n/**\n * @import {State} from 'hast-util-to-mdast'\n * @import {Element} from 'hast'\n * @import {Image} from 'mdast'\n */\n\n/**\n * @param {State} state\n *   State.\n * @param {Readonly<Element>} node\n *   hast element to transform.\n * @returns {Image}\n *   mdast node.\n */\nfunction img(state, node) {\n  const properties = node.properties || {}\n\n  /** @type {Image} */\n  const result = {\n    type: 'image',\n    url: state.resolve(String(properties.src || '') || null),\n    title: properties.title ? String(properties.title) : null,\n    alt: properties.alt ? String(properties.alt) : ''\n  }\n  state.patch(node, result)\n  return result\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L2xpYi9oYW5kbGVycy9pbWcuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0EsWUFBWSxPQUFPO0FBQ25CLFlBQVksU0FBUztBQUNyQixZQUFZLE9BQU87QUFDbkI7O0FBRUE7QUFDQSxXQUFXLE9BQU87QUFDbEI7QUFDQSxXQUFXLG1CQUFtQjtBQUM5QjtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ087QUFDUDs7QUFFQSxhQUFhLE9BQU87QUFDcEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGJlbmppXFxEZXNrdG9wXFxHX1BST0dcXE5pY29sYXNcXGJ0ZGFzaC1lY29zeXN0ZW1cXGJ0ZGFzaHNcXG5vZGVfbW9kdWxlc1xcaGFzdC11dGlsLXRvLW1kYXN0XFxsaWJcXGhhbmRsZXJzXFxpbWcuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAaW1wb3J0IHtTdGF0ZX0gZnJvbSAnaGFzdC11dGlsLXRvLW1kYXN0J1xuICogQGltcG9ydCB7RWxlbWVudH0gZnJvbSAnaGFzdCdcbiAqIEBpbXBvcnQge0ltYWdlfSBmcm9tICdtZGFzdCdcbiAqL1xuXG4vKipcbiAqIEBwYXJhbSB7U3RhdGV9IHN0YXRlXG4gKiAgIFN0YXRlLlxuICogQHBhcmFtIHtSZWFkb25seTxFbGVtZW50Pn0gbm9kZVxuICogICBoYXN0IGVsZW1lbnQgdG8gdHJhbnNmb3JtLlxuICogQHJldHVybnMge0ltYWdlfVxuICogICBtZGFzdCBub2RlLlxuICovXG5leHBvcnQgZnVuY3Rpb24gaW1nKHN0YXRlLCBub2RlKSB7XG4gIGNvbnN0IHByb3BlcnRpZXMgPSBub2RlLnByb3BlcnRpZXMgfHwge31cblxuICAvKiogQHR5cGUge0ltYWdlfSAqL1xuICBjb25zdCByZXN1bHQgPSB7XG4gICAgdHlwZTogJ2ltYWdlJyxcbiAgICB1cmw6IHN0YXRlLnJlc29sdmUoU3RyaW5nKHByb3BlcnRpZXMuc3JjIHx8ICcnKSB8fCBudWxsKSxcbiAgICB0aXRsZTogcHJvcGVydGllcy50aXRsZSA/IFN0cmluZyhwcm9wZXJ0aWVzLnRpdGxlKSA6IG51bGwsXG4gICAgYWx0OiBwcm9wZXJ0aWVzLmFsdCA/IFN0cmluZyhwcm9wZXJ0aWVzLmFsdCkgOiAnJ1xuICB9XG4gIHN0YXRlLnBhdGNoKG5vZGUsIHJlc3VsdClcbiAgcmV0dXJuIHJlc3VsdFxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/img.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/index.js":
/*!***************************************************************!*\
  !*** ./node_modules/hast-util-to-mdast/lib/handlers/index.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   handlers: () => (/* binding */ handlers),\n/* harmony export */   nodeHandlers: () => (/* binding */ nodeHandlers)\n/* harmony export */ });\n/* harmony import */ var _a_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./a.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/a.js\");\n/* harmony import */ var _base_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./base.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/base.js\");\n/* harmony import */ var _blockquote_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./blockquote.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/blockquote.js\");\n/* harmony import */ var _br_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./br.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/br.js\");\n/* harmony import */ var _code_js__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ./code.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/code.js\");\n/* harmony import */ var _comment_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./comment.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/comment.js\");\n/* harmony import */ var _del_js__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./del.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/del.js\");\n/* harmony import */ var _dl_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./dl.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/dl.js\");\n/* harmony import */ var _em_js__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./em.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/em.js\");\n/* harmony import */ var _heading_js__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./heading.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/heading.js\");\n/* harmony import */ var _hr_js__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./hr.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/hr.js\");\n/* harmony import */ var _iframe_js__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./iframe.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/iframe.js\");\n/* harmony import */ var _img_js__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ./img.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/img.js\");\n/* harmony import */ var _inline_code_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./inline-code.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/inline-code.js\");\n/* harmony import */ var _input_js__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./input.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/input.js\");\n/* harmony import */ var _li_js__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./li.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/li.js\");\n/* harmony import */ var _list_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./list.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/list.js\");\n/* harmony import */ var _media_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./media.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/media.js\");\n/* harmony import */ var _p_js__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! ./p.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/p.js\");\n/* harmony import */ var _q_js__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! ./q.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/q.js\");\n/* harmony import */ var _root_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./root.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/root.js\");\n/* harmony import */ var _select_js__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! ./select.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/select.js\");\n/* harmony import */ var _strong_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./strong.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/strong.js\");\n/* harmony import */ var _table_cell_js__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! ./table-cell.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/table-cell.js\");\n/* harmony import */ var _table_row_js__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! ./table-row.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/table-row.js\");\n/* harmony import */ var _table_js__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! ./table.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/table.js\");\n/* harmony import */ var _text_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./text.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/text.js\");\n/* harmony import */ var _textarea_js__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! ./textarea.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/textarea.js\");\n/* harmony import */ var _wbr_js__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! ./wbr.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/wbr.js\");\n/**\n * @import {State} from 'hast-util-to-mdast'\n * @import {Parents} from 'hast'\n */\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n/**\n * Default handlers for nodes.\n *\n * Each key is a node type, each value is a `NodeHandler`.\n */\nconst nodeHandlers = {\n  comment: _comment_js__WEBPACK_IMPORTED_MODULE_0__.comment,\n  doctype: ignore,\n  root: _root_js__WEBPACK_IMPORTED_MODULE_1__.root,\n  text: _text_js__WEBPACK_IMPORTED_MODULE_2__.text\n}\n\n/**\n * Default handlers for elements.\n *\n * Each key is an element name, each value is a `Handler`.\n */\nconst handlers = {\n  // Ignore:\n  applet: ignore,\n  area: ignore,\n  basefont: ignore,\n  bgsound: ignore,\n  caption: ignore,\n  col: ignore,\n  colgroup: ignore,\n  command: ignore,\n  content: ignore,\n  datalist: ignore,\n  dialog: ignore,\n  element: ignore,\n  embed: ignore,\n  frame: ignore,\n  frameset: ignore,\n  isindex: ignore,\n  keygen: ignore,\n  link: ignore,\n  math: ignore,\n  menu: ignore,\n  menuitem: ignore,\n  meta: ignore,\n  nextid: ignore,\n  noembed: ignore,\n  noframes: ignore,\n  optgroup: ignore,\n  option: ignore,\n  param: ignore,\n  script: ignore,\n  shadow: ignore,\n  source: ignore,\n  spacer: ignore,\n  style: ignore,\n  svg: ignore,\n  template: ignore,\n  title: ignore,\n  track: ignore,\n\n  // Use children:\n  abbr: all,\n  acronym: all,\n  bdi: all,\n  bdo: all,\n  big: all,\n  blink: all,\n  button: all,\n  canvas: all,\n  cite: all,\n  data: all,\n  details: all,\n  dfn: all,\n  font: all,\n  ins: all,\n  label: all,\n  map: all,\n  marquee: all,\n  meter: all,\n  nobr: all,\n  noscript: all,\n  object: all,\n  output: all,\n  progress: all,\n  rb: all,\n  rbc: all,\n  rp: all,\n  rt: all,\n  rtc: all,\n  ruby: all,\n  slot: all,\n  small: all,\n  span: all,\n  sup: all,\n  sub: all,\n  tbody: all,\n  tfoot: all,\n  thead: all,\n  time: all,\n\n  // Use children as flow.\n  address: flow,\n  article: flow,\n  aside: flow,\n  body: flow,\n  center: flow,\n  div: flow,\n  fieldset: flow,\n  figcaption: flow,\n  figure: flow,\n  form: flow,\n  footer: flow,\n  header: flow,\n  hgroup: flow,\n  html: flow,\n  legend: flow,\n  main: flow,\n  multicol: flow,\n  nav: flow,\n  picture: flow,\n  section: flow,\n\n  // Handle.\n  a: _a_js__WEBPACK_IMPORTED_MODULE_3__.a,\n  audio: _media_js__WEBPACK_IMPORTED_MODULE_4__.media,\n  b: _strong_js__WEBPACK_IMPORTED_MODULE_5__.strong,\n  base: _base_js__WEBPACK_IMPORTED_MODULE_6__.base,\n  blockquote: _blockquote_js__WEBPACK_IMPORTED_MODULE_7__.blockquote,\n  br: _br_js__WEBPACK_IMPORTED_MODULE_8__.br,\n  code: _inline_code_js__WEBPACK_IMPORTED_MODULE_9__.inlineCode,\n  dir: _list_js__WEBPACK_IMPORTED_MODULE_10__.list,\n  dl: _dl_js__WEBPACK_IMPORTED_MODULE_11__.dl,\n  dt: _li_js__WEBPACK_IMPORTED_MODULE_12__.li,\n  dd: _li_js__WEBPACK_IMPORTED_MODULE_12__.li,\n  del: _del_js__WEBPACK_IMPORTED_MODULE_13__.del,\n  em: _em_js__WEBPACK_IMPORTED_MODULE_14__.em,\n  h1: _heading_js__WEBPACK_IMPORTED_MODULE_15__.heading,\n  h2: _heading_js__WEBPACK_IMPORTED_MODULE_15__.heading,\n  h3: _heading_js__WEBPACK_IMPORTED_MODULE_15__.heading,\n  h4: _heading_js__WEBPACK_IMPORTED_MODULE_15__.heading,\n  h5: _heading_js__WEBPACK_IMPORTED_MODULE_15__.heading,\n  h6: _heading_js__WEBPACK_IMPORTED_MODULE_15__.heading,\n  hr: _hr_js__WEBPACK_IMPORTED_MODULE_16__.hr,\n  i: _em_js__WEBPACK_IMPORTED_MODULE_14__.em,\n  iframe: _iframe_js__WEBPACK_IMPORTED_MODULE_17__.iframe,\n  img: _img_js__WEBPACK_IMPORTED_MODULE_18__.img,\n  image: _img_js__WEBPACK_IMPORTED_MODULE_18__.img,\n  input: _input_js__WEBPACK_IMPORTED_MODULE_19__.input,\n  kbd: _inline_code_js__WEBPACK_IMPORTED_MODULE_9__.inlineCode,\n  li: _li_js__WEBPACK_IMPORTED_MODULE_12__.li,\n  listing: _code_js__WEBPACK_IMPORTED_MODULE_20__.code,\n  mark: _em_js__WEBPACK_IMPORTED_MODULE_14__.em,\n  ol: _list_js__WEBPACK_IMPORTED_MODULE_10__.list,\n  p: _p_js__WEBPACK_IMPORTED_MODULE_21__.p,\n  plaintext: _code_js__WEBPACK_IMPORTED_MODULE_20__.code,\n  pre: _code_js__WEBPACK_IMPORTED_MODULE_20__.code,\n  q: _q_js__WEBPACK_IMPORTED_MODULE_22__.q,\n  s: _del_js__WEBPACK_IMPORTED_MODULE_13__.del,\n  samp: _inline_code_js__WEBPACK_IMPORTED_MODULE_9__.inlineCode,\n  select: _select_js__WEBPACK_IMPORTED_MODULE_23__.select,\n  strike: _del_js__WEBPACK_IMPORTED_MODULE_13__.del,\n  strong: _strong_js__WEBPACK_IMPORTED_MODULE_5__.strong,\n  summary: _p_js__WEBPACK_IMPORTED_MODULE_21__.p,\n  table: _table_js__WEBPACK_IMPORTED_MODULE_24__.table,\n  td: _table_cell_js__WEBPACK_IMPORTED_MODULE_25__.tableCell,\n  textarea: _textarea_js__WEBPACK_IMPORTED_MODULE_26__.textarea,\n  th: _table_cell_js__WEBPACK_IMPORTED_MODULE_25__.tableCell,\n  tr: _table_row_js__WEBPACK_IMPORTED_MODULE_27__.tableRow,\n  tt: _inline_code_js__WEBPACK_IMPORTED_MODULE_9__.inlineCode,\n  u: _em_js__WEBPACK_IMPORTED_MODULE_14__.em,\n  ul: _list_js__WEBPACK_IMPORTED_MODULE_10__.list,\n  var: _inline_code_js__WEBPACK_IMPORTED_MODULE_9__.inlineCode,\n  video: _media_js__WEBPACK_IMPORTED_MODULE_4__.media,\n  wbr: _wbr_js__WEBPACK_IMPORTED_MODULE_28__.wbr,\n  xmp: _code_js__WEBPACK_IMPORTED_MODULE_20__.code\n}\n\n/**\n * @param {State} state\n *   State.\n * @param {Parents} node\n *   Parent to transform.\n */\nfunction all(state, node) {\n  return state.all(node)\n}\n\n/**\n * @param {State} state\n *   State.\n * @param {Parents} node\n *   Parent to transform.\n */\nfunction flow(state, node) {\n  return state.toFlow(state.all(node))\n}\n\n/**\n * @returns {undefined}\n */\nfunction ignore() {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/inline-code.js":
/*!*********************************************************************!*\
  !*** ./node_modules/hast-util-to-mdast/lib/handlers/inline-code.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   inlineCode: () => (/* binding */ inlineCode)\n/* harmony export */ });\n/* harmony import */ var hast_util_to_text__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! hast-util-to-text */ \"(ssr)/./node_modules/hast-util-to-text/lib/index.js\");\n/**\n * @import {State} from 'hast-util-to-mdast'\n * @import {Element} from 'hast'\n * @import {InlineCode} from 'mdast'\n */\n\n\n\n/**\n * @param {State} state\n *   State.\n * @param {Readonly<Element>} node\n *   hast element to transform.\n * @returns {InlineCode}\n *   mdast node.\n */\nfunction inlineCode(state, node) {\n  /** @type {InlineCode} */\n  const result = {type: 'inlineCode', value: (0,hast_util_to_text__WEBPACK_IMPORTED_MODULE_0__.toText)(node)}\n  state.patch(node, result)\n  return result\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L2xpYi9oYW5kbGVycy9pbmxpbmUtY29kZS5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ0EsWUFBWSxPQUFPO0FBQ25CLFlBQVksU0FBUztBQUNyQixZQUFZLFlBQVk7QUFDeEI7O0FBRXdDOztBQUV4QztBQUNBLFdBQVcsT0FBTztBQUNsQjtBQUNBLFdBQVcsbUJBQW1CO0FBQzlCO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDTztBQUNQLGFBQWEsWUFBWTtBQUN6QixrQkFBa0IsMkJBQTJCLHlEQUFNO0FBQ25EO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxiZW5qaVxcRGVza3RvcFxcR19QUk9HXFxOaWNvbGFzXFxidGRhc2gtZWNvc3lzdGVtXFxidGRhc2hzXFxub2RlX21vZHVsZXNcXGhhc3QtdXRpbC10by1tZGFzdFxcbGliXFxoYW5kbGVyc1xcaW5saW5lLWNvZGUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAaW1wb3J0IHtTdGF0ZX0gZnJvbSAnaGFzdC11dGlsLXRvLW1kYXN0J1xuICogQGltcG9ydCB7RWxlbWVudH0gZnJvbSAnaGFzdCdcbiAqIEBpbXBvcnQge0lubGluZUNvZGV9IGZyb20gJ21kYXN0J1xuICovXG5cbmltcG9ydCB7dG9UZXh0fSBmcm9tICdoYXN0LXV0aWwtdG8tdGV4dCdcblxuLyoqXG4gKiBAcGFyYW0ge1N0YXRlfSBzdGF0ZVxuICogICBTdGF0ZS5cbiAqIEBwYXJhbSB7UmVhZG9ubHk8RWxlbWVudD59IG5vZGVcbiAqICAgaGFzdCBlbGVtZW50IHRvIHRyYW5zZm9ybS5cbiAqIEByZXR1cm5zIHtJbmxpbmVDb2RlfVxuICogICBtZGFzdCBub2RlLlxuICovXG5leHBvcnQgZnVuY3Rpb24gaW5saW5lQ29kZShzdGF0ZSwgbm9kZSkge1xuICAvKiogQHR5cGUge0lubGluZUNvZGV9ICovXG4gIGNvbnN0IHJlc3VsdCA9IHt0eXBlOiAnaW5saW5lQ29kZScsIHZhbHVlOiB0b1RleHQobm9kZSl9XG4gIHN0YXRlLnBhdGNoKG5vZGUsIHJlc3VsdClcbiAgcmV0dXJuIHJlc3VsdFxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/inline-code.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/input.js":
/*!***************************************************************!*\
  !*** ./node_modules/hast-util-to-mdast/lib/handlers/input.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   input: () => (/* binding */ input)\n/* harmony export */ });\n/* harmony import */ var _util_find_selected_options_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/find-selected-options.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/util/find-selected-options.js\");\n/**\n * @import {State} from 'hast-util-to-mdast'\n * @import {Element} from 'hast'\n * @import {Image, Link, Text} from 'mdast'\n * @import {Options} from '../util/find-selected-options.js'\n */\n\n\n\nconst defaultChecked = '[x]'\nconst defaultUnchecked = '[ ]'\n\n/**\n * @param {State} state\n *   State.\n * @param {Readonly<Element>} node\n *   hast element to transform.\n * @returns {Array<Link | Text> | Image | Text | undefined}\n *   mdast node.\n */\n// eslint-disable-next-line complexity\nfunction input(state, node) {\n  const properties = node.properties || {}\n  const value = String(properties.value || properties.placeholder || '')\n\n  if (\n    properties.disabled ||\n    properties.type === 'hidden' ||\n    properties.type === 'file'\n  ) {\n    return\n  }\n\n  if (properties.type === 'checkbox' || properties.type === 'radio') {\n    /** @type {Text} */\n    const result = {\n      type: 'text',\n      value: properties.checked\n        ? state.options.checked || defaultChecked\n        : state.options.unchecked || defaultUnchecked\n    }\n    state.patch(node, result)\n    return result\n  }\n\n  if (properties.type === 'image') {\n    const alt = properties.alt || value\n\n    if (alt) {\n      /** @type {Image} */\n      const result = {\n        type: 'image',\n        url: state.resolve(String(properties.src || '') || null),\n        title: String(properties.title || '') || null,\n        alt: String(alt)\n      }\n      state.patch(node, result)\n      return result\n    }\n\n    return\n  }\n\n  /** @type {Options} */\n  let values = []\n\n  if (value) {\n    values = [[value, undefined]]\n  } else if (\n    // `list` is not supported on these types:\n    properties.type !== 'button' &&\n    properties.type !== 'file' &&\n    properties.type !== 'password' &&\n    properties.type !== 'reset' &&\n    properties.type !== 'submit' &&\n    properties.list\n  ) {\n    const list = String(properties.list)\n    const datalist = state.elementById.get(list)\n\n    if (datalist && datalist.tagName === 'datalist') {\n      values = (0,_util_find_selected_options_js__WEBPACK_IMPORTED_MODULE_0__.findSelectedOptions)(datalist, properties)\n    }\n  }\n\n  if (values.length === 0) {\n    return\n  }\n\n  // Hide password value.\n  if (properties.type === 'password') {\n    // Passwords don’t support `list`.\n    values[0] = ['•'.repeat(values[0][0].length), undefined]\n  }\n\n  if (properties.type === 'email' || properties.type === 'url') {\n    /** @type {Array<Link | Text>} */\n    const results = []\n    let index = -1\n\n    while (++index < values.length) {\n      const value = state.resolve(values[index][0])\n      /** @type {Link} */\n      const result = {\n        type: 'link',\n        title: null,\n        url: properties.type === 'email' ? 'mailto:' + value : value,\n        children: [{type: 'text', value: values[index][1] || value}]\n      }\n\n      results.push(result)\n\n      if (index !== values.length - 1) {\n        results.push({type: 'text', value: ', '})\n      }\n    }\n\n    return results\n  }\n\n  /** @type {Array<string>} */\n  const texts = []\n  let index = -1\n\n  while (++index < values.length) {\n    texts.push(\n      values[index][1]\n        ? values[index][1] + ' (' + values[index][0] + ')'\n        : values[index][0]\n    )\n  }\n\n  /** @type {Text} */\n  const result = {type: 'text', value: texts.join(', ')}\n  state.patch(node, result)\n  return result\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/input.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/li.js":
/*!************************************************************!*\
  !*** ./node_modules/hast-util-to-mdast/lib/handlers/li.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   li: () => (/* binding */ li)\n/* harmony export */ });\n/* harmony import */ var hast_util_phrasing__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! hast-util-phrasing */ \"(ssr)/./node_modules/hast-util-phrasing/lib/index.js\");\n/**\n * @import {State} from 'hast-util-to-mdast'\n * @import {Element} from 'hast'\n * @import {ListItem} from 'mdast'\n */\n\n/**\n * @typedef ExtractResult\n *   Result of extracting a leading checkbox.\n * @property {Element | undefined} checkbox\n *   The checkbox that was removed, if any.\n * @property {Element} rest\n *   If there was a leading checkbox, a deep clone of the node w/o the leading\n *   checkbox; otherwise a reference to the given, untouched, node.\n */\n\n\n\n/**\n * @param {State} state\n *   State.\n * @param {Readonly<Element>} node\n *   hast element to transform.\n * @returns {ListItem}\n *   mdast node.\n */\nfunction li(state, node) {\n  // If the list item starts with a checkbox, remove the checkbox and mark the\n  // list item as a GFM task list item.\n  const {rest, checkbox} = extractLeadingCheckbox(node)\n  const checked = checkbox ? Boolean(checkbox.properties.checked) : null\n  const spread = spreadout(rest)\n  const children = state.toFlow(state.all(rest))\n\n  /** @type {ListItem} */\n  const result = {type: 'listItem', spread, checked, children}\n  state.patch(node, result)\n  return result\n}\n\n/**\n * Check if an element should spread out.\n *\n * The reason to spread out a markdown list item is primarily whether writing\n * the equivalent in markdown, would yield a spread out item.\n *\n * A spread out item results in `<p>` and `</p>` tags.\n * Otherwise, the phrasing would be output directly.\n * We can check for that: if there’s a `<p>` element, spread it out.\n *\n * But what if there are no paragraphs?\n * In that case, we can also assume that if two “block” things were written in\n * an item, that it is spread out, because blocks are typically joined by blank\n * lines, which also means a spread item.\n *\n * Lastly, because in HTML things can be wrapped in a `<div>` or similar, we\n * delve into non-phrasing elements here to figure out if they themselves\n * contain paragraphs or 2 or more flow non-phrasing elements.\n *\n * @param {Readonly<Element>} node\n * @returns {boolean}\n */\nfunction spreadout(node) {\n  let index = -1\n  let seenFlow = false\n\n  while (++index < node.children.length) {\n    const child = node.children[index]\n\n    if (child.type === 'element') {\n      if ((0,hast_util_phrasing__WEBPACK_IMPORTED_MODULE_0__.phrasing)(child)) continue\n\n      if (child.tagName === 'p' || seenFlow || spreadout(child)) {\n        return true\n      }\n\n      seenFlow = true\n    }\n  }\n\n  return false\n}\n\n/**\n * Extract a leading checkbox from a list item.\n *\n * If there was a leading checkbox, makes a deep clone of the node w/o the\n * leading checkbox; otherwise a reference to the given, untouched, node is\n * given back.\n *\n * So for example:\n *\n * ```html\n * <li><input type=\"checkbox\">Text</li>\n * ```\n *\n * …becomes:\n *\n * ```html\n * <li>Text</li>\n * ```\n *\n * ```html\n * <li><p><input type=\"checkbox\">Text</p></li>\n * ```\n *\n * …becomes:\n *\n * ```html\n * <li><p>Text</p></li>\n * ```\n *\n * @param {Readonly<Element>} node\n * @returns {ExtractResult}\n */\nfunction extractLeadingCheckbox(node) {\n  const head = node.children[0]\n\n  if (\n    head &&\n    head.type === 'element' &&\n    head.tagName === 'input' &&\n    head.properties &&\n    (head.properties.type === 'checkbox' || head.properties.type === 'radio')\n  ) {\n    const rest = {...node, children: node.children.slice(1)}\n    return {checkbox: head, rest}\n  }\n\n  // The checkbox may be nested in another element.\n  // If the first element has children, look for a leading checkbox inside it.\n  //\n  // This only handles nesting in `<p>` elements, which is most common.\n  // It’s possible a leading checkbox might be nested in other types of flow or\n  // phrasing elements (and *deeply* nested, which is not possible with `<p>`).\n  // Limiting things to `<p>` elements keeps this simpler for now.\n  if (head && head.type === 'element' && head.tagName === 'p') {\n    const {checkbox, rest: restHead} = extractLeadingCheckbox(head)\n\n    if (checkbox) {\n      const rest = {...node, children: [restHead, ...node.children.slice(1)]}\n      return {checkbox, rest}\n    }\n  }\n\n  return {checkbox: undefined, rest: node}\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/li.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/list.js":
/*!**************************************************************!*\
  !*** ./node_modules/hast-util-to-mdast/lib/handlers/list.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   list: () => (/* binding */ list)\n/* harmony export */ });\n/* harmony import */ var _util_list_items_spread_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/list-items-spread.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/util/list-items-spread.js\");\n/**\n * @import {State} from 'hast-util-to-mdast'\n * @import {Element} from 'hast'\n * @import {ListItem, List} from 'mdast'\n */\n\n\n\n/**\n * @param {State} state\n *   State.\n * @param {Readonly<Element>} node\n *   hast element to transform.\n * @returns {List}\n *   mdast node.\n */\nfunction list(state, node) {\n  const ordered = node.tagName === 'ol'\n  const children = state.toSpecificContent(state.all(node), create)\n  /** @type {number | null} */\n  let start = null\n\n  if (ordered) {\n    start =\n      node.properties && node.properties.start\n        ? Number.parseInt(String(node.properties.start), 10)\n        : 1\n  }\n\n  /** @type {List} */\n  const result = {\n    type: 'list',\n    ordered,\n    start,\n    spread: (0,_util_list_items_spread_js__WEBPACK_IMPORTED_MODULE_0__.listItemsSpread)(children),\n    children\n  }\n  state.patch(node, result)\n  return result\n}\n\n/**\n * @returns {ListItem}\n */\nfunction create() {\n  return {type: 'listItem', spread: false, checked: null, children: []}\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/list.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/media.js":
/*!***************************************************************!*\
  !*** ./node_modules/hast-util-to-mdast/lib/handlers/media.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   media: () => (/* binding */ media)\n/* harmony export */ });\n/* harmony import */ var mdast_util_to_string__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! mdast-util-to-string */ \"(ssr)/./node_modules/mdast-util-to-string/lib/index.js\");\n/* harmony import */ var unist_util_visit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! unist-util-visit */ \"(ssr)/./node_modules/unist-util-visit/lib/index.js\");\n/* harmony import */ var unist_util_visit__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! unist-util-visit */ \"(ssr)/./node_modules/unist-util-visit-parents/lib/index.js\");\n/* harmony import */ var _util_wrap_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../util/wrap.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/util/wrap.js\");\n/**\n * @import {State} from 'hast-util-to-mdast'\n * @import {Element} from 'hast'\n * @import {Image, Link, PhrasingContent, RootContent as MdastRootContent, Root} from 'mdast'\n */\n\n\n\n\n\n/**\n * @param {State} state\n *   State.\n * @param {Readonly<Element>} node\n *   hast element to transform.\n * @returns {Array<MdastRootContent> | Link}\n *   mdast node.\n */\nfunction media(state, node) {\n  const properties = node.properties || {}\n  const poster = node.tagName === 'video' ? String(properties.poster || '') : ''\n  let source = String(properties.src || '')\n  let index = -1\n  let linkInFallbackContent = false\n  let nodes = state.all(node)\n\n  /** @type {Root} */\n  const fragment = {type: 'root', children: nodes}\n\n  ;(0,unist_util_visit__WEBPACK_IMPORTED_MODULE_0__.visit)(fragment, function (node) {\n    if (node.type === 'link') {\n      linkInFallbackContent = true\n      return unist_util_visit__WEBPACK_IMPORTED_MODULE_1__.EXIT\n    }\n  })\n\n  // If the content links to something, or if it’s not phrasing…\n  if (linkInFallbackContent || (0,_util_wrap_js__WEBPACK_IMPORTED_MODULE_2__.wrapNeeded)(nodes)) {\n    return nodes\n  }\n\n  // Find the source.\n  while (!source && ++index < node.children.length) {\n    const child = node.children[index]\n\n    if (\n      child.type === 'element' &&\n      child.tagName === 'source' &&\n      child.properties\n    ) {\n      source = String(child.properties.src || '')\n    }\n  }\n\n  // If there’s a poster defined on the video, create an image.\n  if (poster) {\n    /** @type {Image} */\n    const image = {\n      type: 'image',\n      title: null,\n      url: state.resolve(poster),\n      alt: (0,mdast_util_to_string__WEBPACK_IMPORTED_MODULE_3__.toString)(nodes)\n    }\n    state.patch(node, image)\n    nodes = [image]\n  }\n\n  // Allow potentially “invalid” nodes, they might be unknown.\n  // We also support straddling later.\n  const children = /** @type {Array<PhrasingContent>} */ (nodes)\n\n  // Link to the media resource.\n  /** @type {Link} */\n  const result = {\n    type: 'link',\n    title: properties.title ? String(properties.title) : null,\n    url: state.resolve(source),\n    children\n  }\n  state.patch(node, result)\n  return result\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/media.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/p.js":
/*!***********************************************************!*\
  !*** ./node_modules/hast-util-to-mdast/lib/handlers/p.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   p: () => (/* binding */ p)\n/* harmony export */ });\n/* harmony import */ var _util_drop_surrounding_breaks_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/drop-surrounding-breaks.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/util/drop-surrounding-breaks.js\");\n/**\n * @import {State} from 'hast-util-to-mdast'\n * @import {Element} from 'hast'\n * @import {Paragraph, PhrasingContent} from 'mdast'\n */\n\n\n\n/**\n * @param {State} state\n *   State.\n * @param {Readonly<Element>} node\n *   hast element to transform.\n * @returns {Paragraph | undefined}\n *   mdast node.\n */\nfunction p(state, node) {\n  const children = (0,_util_drop_surrounding_breaks_js__WEBPACK_IMPORTED_MODULE_0__.dropSurroundingBreaks)(\n    // Allow potentially “invalid” nodes, they might be unknown.\n    // We also support straddling later.\n    /** @type {Array<PhrasingContent>} */ (state.all(node))\n  )\n\n  if (children.length > 0) {\n    /** @type {Paragraph} */\n    const result = {type: 'paragraph', children}\n    state.patch(node, result)\n    return result\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L2xpYi9oYW5kbGVycy9wLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDQSxZQUFZLE9BQU87QUFDbkIsWUFBWSxTQUFTO0FBQ3JCLFlBQVksNEJBQTRCO0FBQ3hDOztBQUV3RTs7QUFFeEU7QUFDQSxXQUFXLE9BQU87QUFDbEI7QUFDQSxXQUFXLG1CQUFtQjtBQUM5QjtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ087QUFDUCxtQkFBbUIsdUZBQXFCO0FBQ3hDO0FBQ0E7QUFDQSxlQUFlLHdCQUF3QjtBQUN2Qzs7QUFFQTtBQUNBLGVBQWUsV0FBVztBQUMxQixvQkFBb0I7QUFDcEI7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYmVuamlcXERlc2t0b3BcXEdfUFJPR1xcTmljb2xhc1xcYnRkYXNoLWVjb3N5c3RlbVxcYnRkYXNoc1xcbm9kZV9tb2R1bGVzXFxoYXN0LXV0aWwtdG8tbWRhc3RcXGxpYlxcaGFuZGxlcnNcXHAuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAaW1wb3J0IHtTdGF0ZX0gZnJvbSAnaGFzdC11dGlsLXRvLW1kYXN0J1xuICogQGltcG9ydCB7RWxlbWVudH0gZnJvbSAnaGFzdCdcbiAqIEBpbXBvcnQge1BhcmFncmFwaCwgUGhyYXNpbmdDb250ZW50fSBmcm9tICdtZGFzdCdcbiAqL1xuXG5pbXBvcnQge2Ryb3BTdXJyb3VuZGluZ0JyZWFrc30gZnJvbSAnLi4vdXRpbC9kcm9wLXN1cnJvdW5kaW5nLWJyZWFrcy5qcydcblxuLyoqXG4gKiBAcGFyYW0ge1N0YXRlfSBzdGF0ZVxuICogICBTdGF0ZS5cbiAqIEBwYXJhbSB7UmVhZG9ubHk8RWxlbWVudD59IG5vZGVcbiAqICAgaGFzdCBlbGVtZW50IHRvIHRyYW5zZm9ybS5cbiAqIEByZXR1cm5zIHtQYXJhZ3JhcGggfCB1bmRlZmluZWR9XG4gKiAgIG1kYXN0IG5vZGUuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBwKHN0YXRlLCBub2RlKSB7XG4gIGNvbnN0IGNoaWxkcmVuID0gZHJvcFN1cnJvdW5kaW5nQnJlYWtzKFxuICAgIC8vIEFsbG93IHBvdGVudGlhbGx5IOKAnGludmFsaWTigJ0gbm9kZXMsIHRoZXkgbWlnaHQgYmUgdW5rbm93bi5cbiAgICAvLyBXZSBhbHNvIHN1cHBvcnQgc3RyYWRkbGluZyBsYXRlci5cbiAgICAvKiogQHR5cGUge0FycmF5PFBocmFzaW5nQ29udGVudD59ICovIChzdGF0ZS5hbGwobm9kZSkpXG4gIClcblxuICBpZiAoY2hpbGRyZW4ubGVuZ3RoID4gMCkge1xuICAgIC8qKiBAdHlwZSB7UGFyYWdyYXBofSAqL1xuICAgIGNvbnN0IHJlc3VsdCA9IHt0eXBlOiAncGFyYWdyYXBoJywgY2hpbGRyZW59XG4gICAgc3RhdGUucGF0Y2gobm9kZSwgcmVzdWx0KVxuICAgIHJldHVybiByZXN1bHRcbiAgfVxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/p.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/q.js":
/*!***********************************************************!*\
  !*** ./node_modules/hast-util-to-mdast/lib/handlers/q.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   q: () => (/* binding */ q)\n/* harmony export */ });\n/**\n * @import {State} from 'hast-util-to-mdast'\n * @import {Element} from 'hast'\n * @import {RootContent as MdastRootContent} from 'mdast'\n */\n\nconst defaultQuotes = ['\"']\n\n/**\n * @param {State} state\n *   State.\n * @param {Readonly<Element>} node\n *   hast element to transform.\n * @returns {Array<MdastRootContent>}\n *   mdast nodes.\n */\nfunction q(state, node) {\n  const quotes = state.options.quotes || defaultQuotes\n\n  state.qNesting++\n  const contents = state.all(node)\n  state.qNesting--\n\n  const quote = quotes[state.qNesting % quotes.length]\n  const head = contents[0]\n  const tail = contents[contents.length - 1]\n  const open = quote.charAt(0)\n  const close = quote.length > 1 ? quote.charAt(1) : quote\n\n  if (head && head.type === 'text') {\n    head.value = open + head.value\n  } else {\n    contents.unshift({type: 'text', value: open})\n  }\n\n  if (tail && tail.type === 'text') {\n    tail.value += close\n  } else {\n    contents.push({type: 'text', value: close})\n  }\n\n  return contents\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/q.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/root.js":
/*!**************************************************************!*\
  !*** ./node_modules/hast-util-to-mdast/lib/handlers/root.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   root: () => (/* binding */ root)\n/* harmony export */ });\n/* harmony import */ var _util_wrap_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/wrap.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/util/wrap.js\");\n/**\n * @import {State} from 'hast-util-to-mdast'\n * @import {Root as HastRoot} from 'hast'\n * @import {Root as MdastRoot} from 'mdast'\n */\n\n\n\n/**\n * @param {State} state\n *   State.\n * @param {Readonly<HastRoot>} node\n *   hast root to transform.\n * @returns {MdastRoot}\n *   mdast node.\n */\nfunction root(state, node) {\n  let children = state.all(node)\n\n  if (state.options.document || (0,_util_wrap_js__WEBPACK_IMPORTED_MODULE_0__.wrapNeeded)(children)) {\n    children = (0,_util_wrap_js__WEBPACK_IMPORTED_MODULE_0__.wrap)(children)\n  }\n\n  /** @type {MdastRoot} */\n  const result = {type: 'root', children}\n  state.patch(node, result)\n  return result\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L2xpYi9oYW5kbGVycy9yb290LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDQSxZQUFZLE9BQU87QUFDbkIsWUFBWSxrQkFBa0I7QUFDOUIsWUFBWSxtQkFBbUI7QUFDL0I7O0FBRWdEOztBQUVoRDtBQUNBLFdBQVcsT0FBTztBQUNsQjtBQUNBLFdBQVcsb0JBQW9CO0FBQy9CO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDTztBQUNQOztBQUVBLGdDQUFnQyx5REFBVTtBQUMxQyxlQUFlLG1EQUFJO0FBQ25COztBQUVBLGFBQWEsV0FBVztBQUN4QixrQkFBa0I7QUFDbEI7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGJlbmppXFxEZXNrdG9wXFxHX1BST0dcXE5pY29sYXNcXGJ0ZGFzaC1lY29zeXN0ZW1cXGJ0ZGFzaHNcXG5vZGVfbW9kdWxlc1xcaGFzdC11dGlsLXRvLW1kYXN0XFxsaWJcXGhhbmRsZXJzXFxyb290LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGltcG9ydCB7U3RhdGV9IGZyb20gJ2hhc3QtdXRpbC10by1tZGFzdCdcbiAqIEBpbXBvcnQge1Jvb3QgYXMgSGFzdFJvb3R9IGZyb20gJ2hhc3QnXG4gKiBAaW1wb3J0IHtSb290IGFzIE1kYXN0Um9vdH0gZnJvbSAnbWRhc3QnXG4gKi9cblxuaW1wb3J0IHt3cmFwLCB3cmFwTmVlZGVkfSBmcm9tICcuLi91dGlsL3dyYXAuanMnXG5cbi8qKlxuICogQHBhcmFtIHtTdGF0ZX0gc3RhdGVcbiAqICAgU3RhdGUuXG4gKiBAcGFyYW0ge1JlYWRvbmx5PEhhc3RSb290Pn0gbm9kZVxuICogICBoYXN0IHJvb3QgdG8gdHJhbnNmb3JtLlxuICogQHJldHVybnMge01kYXN0Um9vdH1cbiAqICAgbWRhc3Qgbm9kZS5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIHJvb3Qoc3RhdGUsIG5vZGUpIHtcbiAgbGV0IGNoaWxkcmVuID0gc3RhdGUuYWxsKG5vZGUpXG5cbiAgaWYgKHN0YXRlLm9wdGlvbnMuZG9jdW1lbnQgfHwgd3JhcE5lZWRlZChjaGlsZHJlbikpIHtcbiAgICBjaGlsZHJlbiA9IHdyYXAoY2hpbGRyZW4pXG4gIH1cblxuICAvKiogQHR5cGUge01kYXN0Um9vdH0gKi9cbiAgY29uc3QgcmVzdWx0ID0ge3R5cGU6ICdyb290JywgY2hpbGRyZW59XG4gIHN0YXRlLnBhdGNoKG5vZGUsIHJlc3VsdClcbiAgcmV0dXJuIHJlc3VsdFxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/root.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/select.js":
/*!****************************************************************!*\
  !*** ./node_modules/hast-util-to-mdast/lib/handlers/select.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   select: () => (/* binding */ select)\n/* harmony export */ });\n/* harmony import */ var _util_find_selected_options_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/find-selected-options.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/util/find-selected-options.js\");\n/**\n * @import {State} from 'hast-util-to-mdast'\n * @import {Element} from 'hast'\n * @import {Text} from 'mdast'\n */\n\n\n\n/**\n * @param {State} state\n *   State.\n * @param {Readonly<Element>} node\n *   hast element to transform.\n * @returns {Text | undefined}\n *   mdast node.\n */\nfunction select(state, node) {\n  const values = (0,_util_find_selected_options_js__WEBPACK_IMPORTED_MODULE_0__.findSelectedOptions)(node)\n  let index = -1\n  /** @type {Array<string>} */\n  const results = []\n\n  while (++index < values.length) {\n    const value = values[index]\n    results.push(value[1] ? value[1] + ' (' + value[0] + ')' : value[0])\n  }\n\n  if (results.length > 0) {\n    /** @type {Text} */\n    const result = {type: 'text', value: results.join(', ')}\n    state.patch(node, result)\n    return result\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L2xpYi9oYW5kbGVycy9zZWxlY3QuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNBLFlBQVksT0FBTztBQUNuQixZQUFZLFNBQVM7QUFDckIsWUFBWSxNQUFNO0FBQ2xCOztBQUVvRTs7QUFFcEU7QUFDQSxXQUFXLE9BQU87QUFDbEI7QUFDQSxXQUFXLG1CQUFtQjtBQUM5QjtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ087QUFDUCxpQkFBaUIsbUZBQW1CO0FBQ3BDO0FBQ0EsYUFBYSxlQUFlO0FBQzVCOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EsZUFBZSxNQUFNO0FBQ3JCLG9CQUFvQjtBQUNwQjtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxiZW5qaVxcRGVza3RvcFxcR19QUk9HXFxOaWNvbGFzXFxidGRhc2gtZWNvc3lzdGVtXFxidGRhc2hzXFxub2RlX21vZHVsZXNcXGhhc3QtdXRpbC10by1tZGFzdFxcbGliXFxoYW5kbGVyc1xcc2VsZWN0LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGltcG9ydCB7U3RhdGV9IGZyb20gJ2hhc3QtdXRpbC10by1tZGFzdCdcbiAqIEBpbXBvcnQge0VsZW1lbnR9IGZyb20gJ2hhc3QnXG4gKiBAaW1wb3J0IHtUZXh0fSBmcm9tICdtZGFzdCdcbiAqL1xuXG5pbXBvcnQge2ZpbmRTZWxlY3RlZE9wdGlvbnN9IGZyb20gJy4uL3V0aWwvZmluZC1zZWxlY3RlZC1vcHRpb25zLmpzJ1xuXG4vKipcbiAqIEBwYXJhbSB7U3RhdGV9IHN0YXRlXG4gKiAgIFN0YXRlLlxuICogQHBhcmFtIHtSZWFkb25seTxFbGVtZW50Pn0gbm9kZVxuICogICBoYXN0IGVsZW1lbnQgdG8gdHJhbnNmb3JtLlxuICogQHJldHVybnMge1RleHQgfCB1bmRlZmluZWR9XG4gKiAgIG1kYXN0IG5vZGUuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBzZWxlY3Qoc3RhdGUsIG5vZGUpIHtcbiAgY29uc3QgdmFsdWVzID0gZmluZFNlbGVjdGVkT3B0aW9ucyhub2RlKVxuICBsZXQgaW5kZXggPSAtMVxuICAvKiogQHR5cGUge0FycmF5PHN0cmluZz59ICovXG4gIGNvbnN0IHJlc3VsdHMgPSBbXVxuXG4gIHdoaWxlICgrK2luZGV4IDwgdmFsdWVzLmxlbmd0aCkge1xuICAgIGNvbnN0IHZhbHVlID0gdmFsdWVzW2luZGV4XVxuICAgIHJlc3VsdHMucHVzaCh2YWx1ZVsxXSA/IHZhbHVlWzFdICsgJyAoJyArIHZhbHVlWzBdICsgJyknIDogdmFsdWVbMF0pXG4gIH1cblxuICBpZiAocmVzdWx0cy5sZW5ndGggPiAwKSB7XG4gICAgLyoqIEB0eXBlIHtUZXh0fSAqL1xuICAgIGNvbnN0IHJlc3VsdCA9IHt0eXBlOiAndGV4dCcsIHZhbHVlOiByZXN1bHRzLmpvaW4oJywgJyl9XG4gICAgc3RhdGUucGF0Y2gobm9kZSwgcmVzdWx0KVxuICAgIHJldHVybiByZXN1bHRcbiAgfVxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/select.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/strong.js":
/*!****************************************************************!*\
  !*** ./node_modules/hast-util-to-mdast/lib/handlers/strong.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   strong: () => (/* binding */ strong)\n/* harmony export */ });\n/**\n * @import {State} from 'hast-util-to-mdast'\n * @import {Element} from 'hast'\n * @import {PhrasingContent, Strong} from 'mdast'\n */\n\n/**\n * @param {State} state\n *   State.\n * @param {Readonly<Element>} node\n *   hast element to transform.\n * @returns {Strong}\n *   mdast node.\n */\nfunction strong(state, node) {\n  // Allow potentially “invalid” nodes, they might be unknown.\n  // We also support straddling later.\n  const children = /** @type {Array<PhrasingContent>} */ (state.all(node))\n\n  /** @type {Strong} */\n  const result = {type: 'strong', children}\n  state.patch(node, result)\n  return result\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L2xpYi9oYW5kbGVycy9zdHJvbmcuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0EsWUFBWSxPQUFPO0FBQ25CLFlBQVksU0FBUztBQUNyQixZQUFZLHlCQUF5QjtBQUNyQzs7QUFFQTtBQUNBLFdBQVcsT0FBTztBQUNsQjtBQUNBLFdBQVcsbUJBQW1CO0FBQzlCO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQSw4QkFBOEIsd0JBQXdCOztBQUV0RCxhQUFhLFFBQVE7QUFDckIsa0JBQWtCO0FBQ2xCO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxiZW5qaVxcRGVza3RvcFxcR19QUk9HXFxOaWNvbGFzXFxidGRhc2gtZWNvc3lzdGVtXFxidGRhc2hzXFxub2RlX21vZHVsZXNcXGhhc3QtdXRpbC10by1tZGFzdFxcbGliXFxoYW5kbGVyc1xcc3Ryb25nLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGltcG9ydCB7U3RhdGV9IGZyb20gJ2hhc3QtdXRpbC10by1tZGFzdCdcbiAqIEBpbXBvcnQge0VsZW1lbnR9IGZyb20gJ2hhc3QnXG4gKiBAaW1wb3J0IHtQaHJhc2luZ0NvbnRlbnQsIFN0cm9uZ30gZnJvbSAnbWRhc3QnXG4gKi9cblxuLyoqXG4gKiBAcGFyYW0ge1N0YXRlfSBzdGF0ZVxuICogICBTdGF0ZS5cbiAqIEBwYXJhbSB7UmVhZG9ubHk8RWxlbWVudD59IG5vZGVcbiAqICAgaGFzdCBlbGVtZW50IHRvIHRyYW5zZm9ybS5cbiAqIEByZXR1cm5zIHtTdHJvbmd9XG4gKiAgIG1kYXN0IG5vZGUuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBzdHJvbmcoc3RhdGUsIG5vZGUpIHtcbiAgLy8gQWxsb3cgcG90ZW50aWFsbHkg4oCcaW52YWxpZOKAnSBub2RlcywgdGhleSBtaWdodCBiZSB1bmtub3duLlxuICAvLyBXZSBhbHNvIHN1cHBvcnQgc3RyYWRkbGluZyBsYXRlci5cbiAgY29uc3QgY2hpbGRyZW4gPSAvKiogQHR5cGUge0FycmF5PFBocmFzaW5nQ29udGVudD59ICovIChzdGF0ZS5hbGwobm9kZSkpXG5cbiAgLyoqIEB0eXBlIHtTdHJvbmd9ICovXG4gIGNvbnN0IHJlc3VsdCA9IHt0eXBlOiAnc3Ryb25nJywgY2hpbGRyZW59XG4gIHN0YXRlLnBhdGNoKG5vZGUsIHJlc3VsdClcbiAgcmV0dXJuIHJlc3VsdFxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/strong.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/table-cell.js":
/*!********************************************************************!*\
  !*** ./node_modules/hast-util-to-mdast/lib/handlers/table-cell.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   tableCell: () => (/* binding */ tableCell)\n/* harmony export */ });\n/**\n * @import {State} from 'hast-util-to-mdast'\n * @import {Element} from 'hast'\n * @import {PhrasingContent, TableCell} from 'mdast'\n */\n\n/**\n * @param {State} state\n *   State.\n * @param {Readonly<Element>} node\n *   hast element to transform.\n * @returns {TableCell}\n *   mdast node.\n */\nfunction tableCell(state, node) {\n  // Allow potentially “invalid” nodes, they might be unknown.\n  // We also support straddling later.\n  const children = /** @type {Array<PhrasingContent>} */ (state.all(node))\n\n  /** @type {TableCell} */\n  const result = {type: 'tableCell', children}\n  state.patch(node, result)\n\n  if (node.properties) {\n    const rowSpan = node.properties.rowSpan\n    const colSpan = node.properties.colSpan\n\n    if (rowSpan || colSpan) {\n      const data = /** @type {Record<string, unknown>} */ (\n        result.data || (result.data = {})\n      )\n      if (rowSpan) data.hastUtilToMdastTemporaryRowSpan = rowSpan\n      if (colSpan) data.hastUtilToMdastTemporaryColSpan = colSpan\n    }\n  }\n\n  return result\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/table-cell.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/table-row.js":
/*!*******************************************************************!*\
  !*** ./node_modules/hast-util-to-mdast/lib/handlers/table-row.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   tableRow: () => (/* binding */ tableRow)\n/* harmony export */ });\n/**\n * @import {State} from 'hast-util-to-mdast'\n * @import {Element} from 'hast'\n * @import {RowContent, TableRow} from 'mdast'\n */\n\n/**\n * @param {State} state\n *   State.\n * @param {Readonly<Element>} node\n *   hast element to transform.\n * @returns {TableRow}\n *   mdast node.\n */\nfunction tableRow(state, node) {\n  const children = state.toSpecificContent(state.all(node), create)\n\n  /** @type {TableRow} */\n  const result = {type: 'tableRow', children}\n  state.patch(node, result)\n  return result\n}\n\n/**\n * @returns {RowContent}\n */\nfunction create() {\n  return {type: 'tableCell', children: []}\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L2xpYi9oYW5kbGVycy90YWJsZS1yb3cuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0EsWUFBWSxPQUFPO0FBQ25CLFlBQVksU0FBUztBQUNyQixZQUFZLHNCQUFzQjtBQUNsQzs7QUFFQTtBQUNBLFdBQVcsT0FBTztBQUNsQjtBQUNBLFdBQVcsbUJBQW1CO0FBQzlCO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDTztBQUNQOztBQUVBLGFBQWEsVUFBVTtBQUN2QixrQkFBa0I7QUFDbEI7QUFDQTtBQUNBOztBQUVBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQSxVQUFVO0FBQ1YiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYmVuamlcXERlc2t0b3BcXEdfUFJPR1xcTmljb2xhc1xcYnRkYXNoLWVjb3N5c3RlbVxcYnRkYXNoc1xcbm9kZV9tb2R1bGVzXFxoYXN0LXV0aWwtdG8tbWRhc3RcXGxpYlxcaGFuZGxlcnNcXHRhYmxlLXJvdy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBpbXBvcnQge1N0YXRlfSBmcm9tICdoYXN0LXV0aWwtdG8tbWRhc3QnXG4gKiBAaW1wb3J0IHtFbGVtZW50fSBmcm9tICdoYXN0J1xuICogQGltcG9ydCB7Um93Q29udGVudCwgVGFibGVSb3d9IGZyb20gJ21kYXN0J1xuICovXG5cbi8qKlxuICogQHBhcmFtIHtTdGF0ZX0gc3RhdGVcbiAqICAgU3RhdGUuXG4gKiBAcGFyYW0ge1JlYWRvbmx5PEVsZW1lbnQ+fSBub2RlXG4gKiAgIGhhc3QgZWxlbWVudCB0byB0cmFuc2Zvcm0uXG4gKiBAcmV0dXJucyB7VGFibGVSb3d9XG4gKiAgIG1kYXN0IG5vZGUuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiB0YWJsZVJvdyhzdGF0ZSwgbm9kZSkge1xuICBjb25zdCBjaGlsZHJlbiA9IHN0YXRlLnRvU3BlY2lmaWNDb250ZW50KHN0YXRlLmFsbChub2RlKSwgY3JlYXRlKVxuXG4gIC8qKiBAdHlwZSB7VGFibGVSb3d9ICovXG4gIGNvbnN0IHJlc3VsdCA9IHt0eXBlOiAndGFibGVSb3cnLCBjaGlsZHJlbn1cbiAgc3RhdGUucGF0Y2gobm9kZSwgcmVzdWx0KVxuICByZXR1cm4gcmVzdWx0XG59XG5cbi8qKlxuICogQHJldHVybnMge1Jvd0NvbnRlbnR9XG4gKi9cbmZ1bmN0aW9uIGNyZWF0ZSgpIHtcbiAgcmV0dXJuIHt0eXBlOiAndGFibGVDZWxsJywgY2hpbGRyZW46IFtdfVxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/table-row.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/table.js":
/*!***************************************************************!*\
  !*** ./node_modules/hast-util-to-mdast/lib/handlers/table.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   table: () => (/* binding */ table)\n/* harmony export */ });\n/* harmony import */ var hast_util_to_text__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! hast-util-to-text */ \"(ssr)/./node_modules/hast-util-to-text/lib/index.js\");\n/* harmony import */ var unist_util_visit__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! unist-util-visit */ \"(ssr)/./node_modules/unist-util-visit/lib/index.js\");\n/* harmony import */ var unist_util_visit__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! unist-util-visit */ \"(ssr)/./node_modules/unist-util-visit-parents/lib/index.js\");\n/**\n * @import {State} from 'hast-util-to-mdast'\n * @import {Element} from 'hast'\n * @import {AlignType, RowContent, TableContent, Table, Text} from 'mdast'\n */\n\n/**\n * @typedef Info\n *   Inferred info on a table.\n * @property {Array<AlignType>} align\n *   Alignment.\n * @property {boolean} headless\n *   Whether a `thead` is missing.\n */\n\n\n\n\n/**\n * @param {State} state\n *   State.\n * @param {Readonly<Element>} node\n *   hast element to transform.\n * @returns {Table | Text}\n *   mdast node.\n */\n// eslint-disable-next-line complexity\nfunction table(state, node) {\n  // Ignore nested tables.\n  if (state.inTable) {\n    /** @type {Text} */\n    const result = {type: 'text', value: (0,hast_util_to_text__WEBPACK_IMPORTED_MODULE_0__.toText)(node)}\n    state.patch(node, result)\n    return result\n  }\n\n  state.inTable = true\n\n  const {align, headless} = inspect(node)\n  const rows = state.toSpecificContent(state.all(node), createRow)\n\n  // Add an empty header row.\n  if (headless) {\n    rows.unshift(createRow())\n  }\n\n  let rowIndex = -1\n\n  while (++rowIndex < rows.length) {\n    const row = rows[rowIndex]\n    const cells = state.toSpecificContent(row.children, createCell)\n    row.children = cells\n  }\n\n  let columns = 1\n  rowIndex = -1\n\n  while (++rowIndex < rows.length) {\n    const cells = rows[rowIndex].children\n    let cellIndex = -1\n\n    while (++cellIndex < cells.length) {\n      const cell = cells[cellIndex]\n\n      if (cell.data) {\n        const data = /** @type {Record<string, unknown>} */ (cell.data)\n        const colSpan =\n          Number.parseInt(String(data.hastUtilToMdastTemporaryColSpan), 10) || 1\n        const rowSpan =\n          Number.parseInt(String(data.hastUtilToMdastTemporaryRowSpan), 10) || 1\n\n        if (colSpan > 1 || rowSpan > 1) {\n          let otherRowIndex = rowIndex - 1\n\n          while (++otherRowIndex < rowIndex + rowSpan) {\n            let colIndex = cellIndex - 1\n\n            while (++colIndex < cellIndex + colSpan) {\n              if (!rows[otherRowIndex]) {\n                // Don’t add rows that don’t exist.\n                // Browsers don’t render them either.\n                break\n              }\n\n              /** @type {Array<RowContent>} */\n              const newCells = []\n\n              if (otherRowIndex !== rowIndex || colIndex !== cellIndex) {\n                newCells.push({type: 'tableCell', children: []})\n              }\n\n              rows[otherRowIndex].children.splice(colIndex, 0, ...newCells)\n            }\n          }\n        }\n\n        // Clean the data fields.\n        if ('hastUtilToMdastTemporaryColSpan' in cell.data)\n          delete cell.data.hastUtilToMdastTemporaryColSpan\n        if ('hastUtilToMdastTemporaryRowSpan' in cell.data)\n          delete cell.data.hastUtilToMdastTemporaryRowSpan\n        if (Object.keys(cell.data).length === 0) delete cell.data\n      }\n    }\n\n    if (cells.length > columns) columns = cells.length\n  }\n\n  // Add extra empty cells.\n  rowIndex = -1\n\n  while (++rowIndex < rows.length) {\n    const cells = rows[rowIndex].children\n    let cellIndex = cells.length - 1\n    while (++cellIndex < columns) {\n      cells.push({type: 'tableCell', children: []})\n    }\n  }\n\n  let alignIndex = align.length - 1\n  while (++alignIndex < columns) {\n    align.push(null)\n  }\n\n  state.inTable = false\n\n  /** @type {Table} */\n  const result = {type: 'table', align, children: rows}\n  state.patch(node, result)\n  return result\n}\n\n/**\n * Infer whether the HTML table has a head and how it aligns.\n *\n * @param {Readonly<Element>} node\n *   Table element to check.\n * @returns {Info}\n *   Info.\n */\nfunction inspect(node) {\n  /** @type {Info} */\n  const info = {align: [null], headless: true}\n  let rowIndex = 0\n  let cellIndex = 0\n\n  ;(0,unist_util_visit__WEBPACK_IMPORTED_MODULE_1__.visit)(node, function (child) {\n    if (child.type === 'element') {\n      // Don’t enter nested tables.\n      if (child.tagName === 'table' && node !== child) {\n        return unist_util_visit__WEBPACK_IMPORTED_MODULE_2__.SKIP\n      }\n\n      if (\n        (child.tagName === 'th' || child.tagName === 'td') &&\n        child.properties\n      ) {\n        if (!info.align[cellIndex]) {\n          const value = String(child.properties.align || '') || null\n\n          if (\n            value === 'center' ||\n            value === 'left' ||\n            value === 'right' ||\n            value === null\n          ) {\n            info.align[cellIndex] = value\n          }\n        }\n\n        // If there is a `th` in the first row, assume there is a header row.\n        if (info.headless && rowIndex < 2 && child.tagName === 'th') {\n          info.headless = false\n        }\n\n        cellIndex++\n      }\n      // If there is a `thead`, assume there is a header row.\n      else if (child.tagName === 'thead') {\n        info.headless = false\n      } else if (child.tagName === 'tr') {\n        rowIndex++\n        cellIndex = 0\n      }\n    }\n  })\n\n  return info\n}\n\n/**\n * @returns {RowContent}\n */\nfunction createCell() {\n  return {type: 'tableCell', children: []}\n}\n\n/**\n * @returns {TableContent}\n */\nfunction createRow() {\n  return {type: 'tableRow', children: []}\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/table.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/text.js":
/*!**************************************************************!*\
  !*** ./node_modules/hast-util-to-mdast/lib/handlers/text.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   text: () => (/* binding */ text)\n/* harmony export */ });\n/**\n * @import {State} from 'hast-util-to-mdast'\n * @import {Text as HastText} from 'hast'\n * @import {Text as MdastText} from 'mdast'\n */\n\n/**\n * @param {State} state\n *   State.\n * @param {Readonly<HastText>} node\n *   hast element to transform.\n * @returns {MdastText}\n *   mdast node.\n */\nfunction text(state, node) {\n  /** @type {MdastText} */\n  const result = {type: 'text', value: node.value}\n  state.patch(node, result)\n  return result\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L2xpYi9oYW5kbGVycy90ZXh0LmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBLFlBQVksT0FBTztBQUNuQixZQUFZLGtCQUFrQjtBQUM5QixZQUFZLG1CQUFtQjtBQUMvQjs7QUFFQTtBQUNBLFdBQVcsT0FBTztBQUNsQjtBQUNBLFdBQVcsb0JBQW9CO0FBQy9CO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDTztBQUNQLGFBQWEsV0FBVztBQUN4QixrQkFBa0I7QUFDbEI7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGJlbmppXFxEZXNrdG9wXFxHX1BST0dcXE5pY29sYXNcXGJ0ZGFzaC1lY29zeXN0ZW1cXGJ0ZGFzaHNcXG5vZGVfbW9kdWxlc1xcaGFzdC11dGlsLXRvLW1kYXN0XFxsaWJcXGhhbmRsZXJzXFx0ZXh0LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGltcG9ydCB7U3RhdGV9IGZyb20gJ2hhc3QtdXRpbC10by1tZGFzdCdcbiAqIEBpbXBvcnQge1RleHQgYXMgSGFzdFRleHR9IGZyb20gJ2hhc3QnXG4gKiBAaW1wb3J0IHtUZXh0IGFzIE1kYXN0VGV4dH0gZnJvbSAnbWRhc3QnXG4gKi9cblxuLyoqXG4gKiBAcGFyYW0ge1N0YXRlfSBzdGF0ZVxuICogICBTdGF0ZS5cbiAqIEBwYXJhbSB7UmVhZG9ubHk8SGFzdFRleHQ+fSBub2RlXG4gKiAgIGhhc3QgZWxlbWVudCB0byB0cmFuc2Zvcm0uXG4gKiBAcmV0dXJucyB7TWRhc3RUZXh0fVxuICogICBtZGFzdCBub2RlLlxuICovXG5leHBvcnQgZnVuY3Rpb24gdGV4dChzdGF0ZSwgbm9kZSkge1xuICAvKiogQHR5cGUge01kYXN0VGV4dH0gKi9cbiAgY29uc3QgcmVzdWx0ID0ge3R5cGU6ICd0ZXh0JywgdmFsdWU6IG5vZGUudmFsdWV9XG4gIHN0YXRlLnBhdGNoKG5vZGUsIHJlc3VsdClcbiAgcmV0dXJuIHJlc3VsdFxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/text.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/textarea.js":
/*!******************************************************************!*\
  !*** ./node_modules/hast-util-to-mdast/lib/handlers/textarea.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   textarea: () => (/* binding */ textarea)\n/* harmony export */ });\n/* harmony import */ var hast_util_to_text__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! hast-util-to-text */ \"(ssr)/./node_modules/hast-util-to-text/lib/index.js\");\n/**\n * @import {State} from 'hast-util-to-mdast'\n * @import {Element} from 'hast'\n * @import {Text} from 'mdast'\n */\n\n\n\n/**\n * @param {State} state\n *   State.\n * @param {Readonly<Element>} node\n *   hast element to transform.\n * @returns {Text}\n *   mdast node.\n */\nfunction textarea(state, node) {\n  /** @type {Text} */\n  const result = {type: 'text', value: (0,hast_util_to_text__WEBPACK_IMPORTED_MODULE_0__.toText)(node)}\n  state.patch(node, result)\n  return result\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L2xpYi9oYW5kbGVycy90ZXh0YXJlYS5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ0EsWUFBWSxPQUFPO0FBQ25CLFlBQVksU0FBUztBQUNyQixZQUFZLE1BQU07QUFDbEI7O0FBRXdDOztBQUV4QztBQUNBLFdBQVcsT0FBTztBQUNsQjtBQUNBLFdBQVcsbUJBQW1CO0FBQzlCO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDTztBQUNQLGFBQWEsTUFBTTtBQUNuQixrQkFBa0IscUJBQXFCLHlEQUFNO0FBQzdDO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxiZW5qaVxcRGVza3RvcFxcR19QUk9HXFxOaWNvbGFzXFxidGRhc2gtZWNvc3lzdGVtXFxidGRhc2hzXFxub2RlX21vZHVsZXNcXGhhc3QtdXRpbC10by1tZGFzdFxcbGliXFxoYW5kbGVyc1xcdGV4dGFyZWEuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAaW1wb3J0IHtTdGF0ZX0gZnJvbSAnaGFzdC11dGlsLXRvLW1kYXN0J1xuICogQGltcG9ydCB7RWxlbWVudH0gZnJvbSAnaGFzdCdcbiAqIEBpbXBvcnQge1RleHR9IGZyb20gJ21kYXN0J1xuICovXG5cbmltcG9ydCB7dG9UZXh0fSBmcm9tICdoYXN0LXV0aWwtdG8tdGV4dCdcblxuLyoqXG4gKiBAcGFyYW0ge1N0YXRlfSBzdGF0ZVxuICogICBTdGF0ZS5cbiAqIEBwYXJhbSB7UmVhZG9ubHk8RWxlbWVudD59IG5vZGVcbiAqICAgaGFzdCBlbGVtZW50IHRvIHRyYW5zZm9ybS5cbiAqIEByZXR1cm5zIHtUZXh0fVxuICogICBtZGFzdCBub2RlLlxuICovXG5leHBvcnQgZnVuY3Rpb24gdGV4dGFyZWEoc3RhdGUsIG5vZGUpIHtcbiAgLyoqIEB0eXBlIHtUZXh0fSAqL1xuICBjb25zdCByZXN1bHQgPSB7dHlwZTogJ3RleHQnLCB2YWx1ZTogdG9UZXh0KG5vZGUpfVxuICBzdGF0ZS5wYXRjaChub2RlLCByZXN1bHQpXG4gIHJldHVybiByZXN1bHRcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/textarea.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/wbr.js":
/*!*************************************************************!*\
  !*** ./node_modules/hast-util-to-mdast/lib/handlers/wbr.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   wbr: () => (/* binding */ wbr)\n/* harmony export */ });\n/**\n * @import {State} from 'hast-util-to-mdast'\n * @import {Element} from 'hast'\n * @import {Text} from 'mdast'\n */\n\n/**\n * @param {State} state\n *   State.\n * @param {Readonly<Element>} node\n *   hast element to transform.\n * @returns {Text}\n *   mdast node.\n */\nfunction wbr(state, node) {\n  /** @type {Text} */\n  const result = {type: 'text', value: '\\u200B'}\n  state.patch(node, result)\n  return result\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L2xpYi9oYW5kbGVycy93YnIuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0EsWUFBWSxPQUFPO0FBQ25CLFlBQVksU0FBUztBQUNyQixZQUFZLE1BQU07QUFDbEI7O0FBRUE7QUFDQSxXQUFXLE9BQU87QUFDbEI7QUFDQSxXQUFXLG1CQUFtQjtBQUM5QjtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ087QUFDUCxhQUFhLE1BQU07QUFDbkIsa0JBQWtCO0FBQ2xCO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxiZW5qaVxcRGVza3RvcFxcR19QUk9HXFxOaWNvbGFzXFxidGRhc2gtZWNvc3lzdGVtXFxidGRhc2hzXFxub2RlX21vZHVsZXNcXGhhc3QtdXRpbC10by1tZGFzdFxcbGliXFxoYW5kbGVyc1xcd2JyLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGltcG9ydCB7U3RhdGV9IGZyb20gJ2hhc3QtdXRpbC10by1tZGFzdCdcbiAqIEBpbXBvcnQge0VsZW1lbnR9IGZyb20gJ2hhc3QnXG4gKiBAaW1wb3J0IHtUZXh0fSBmcm9tICdtZGFzdCdcbiAqL1xuXG4vKipcbiAqIEBwYXJhbSB7U3RhdGV9IHN0YXRlXG4gKiAgIFN0YXRlLlxuICogQHBhcmFtIHtSZWFkb25seTxFbGVtZW50Pn0gbm9kZVxuICogICBoYXN0IGVsZW1lbnQgdG8gdHJhbnNmb3JtLlxuICogQHJldHVybnMge1RleHR9XG4gKiAgIG1kYXN0IG5vZGUuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiB3YnIoc3RhdGUsIG5vZGUpIHtcbiAgLyoqIEB0eXBlIHtUZXh0fSAqL1xuICBjb25zdCByZXN1bHQgPSB7dHlwZTogJ3RleHQnLCB2YWx1ZTogJ1xcdTIwMEInfVxuICBzdGF0ZS5wYXRjaChub2RlLCByZXN1bHQpXG4gIHJldHVybiByZXN1bHRcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/wbr.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-mdast/lib/index.js":
/*!******************************************************!*\
  !*** ./node_modules/hast-util-to-mdast/lib/index.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   toMdast: () => (/* binding */ toMdast)\n/* harmony export */ });\n/* harmony import */ var _ungap_structured_clone__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @ungap/structured-clone */ \"(ssr)/./node_modules/@ungap/structured-clone/esm/index.js\");\n/* harmony import */ var rehype_minify_whitespace__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! rehype-minify-whitespace */ \"(ssr)/./node_modules/rehype-minify-whitespace/lib/index.js\");\n/* harmony import */ var unist_util_visit__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! unist-util-visit */ \"(ssr)/./node_modules/unist-util-visit/lib/index.js\");\n/* harmony import */ var _state_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./state.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/state.js\");\n/**\n * @import {Options} from 'hast-util-to-mdast'\n * @import {Nodes} from 'hast'\n * @import {Nodes as MdastNodes, RootContent as MdastRootContent} from 'mdast'\n */\n\n\n\n\n\n\n/** @type {Readonly<Options>} */\nconst emptyOptions = {}\n\n/**\n * Transform hast to mdast.\n *\n * @param {Readonly<Nodes>} tree\n *   hast tree to transform.\n * @param {Readonly<Options> | null | undefined} [options]\n *   Configuration (optional).\n * @returns {MdastNodes}\n *   mdast tree.\n */\nfunction toMdast(tree, options) {\n  // We have to clone, cause we’ll use `rehype-minify-whitespace` on the tree,\n  // which modifies.\n  const cleanTree = (0,_ungap_structured_clone__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(tree)\n  const settings = options || emptyOptions\n  const transformWhitespace = (0,rehype_minify_whitespace__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n    newlines: settings.newlines === true\n  })\n  const state = (0,_state_js__WEBPACK_IMPORTED_MODULE_2__.createState)(settings)\n  /** @type {MdastNodes} */\n  let mdast\n\n  // @ts-expect-error: fine to pass an arbitrary node.\n  transformWhitespace(cleanTree)\n\n  ;(0,unist_util_visit__WEBPACK_IMPORTED_MODULE_3__.visit)(cleanTree, function (node) {\n    if (node && node.type === 'element' && node.properties) {\n      const id = String(node.properties.id || '') || undefined\n\n      if (id && !state.elementById.has(id)) {\n        state.elementById.set(id, node)\n      }\n    }\n  })\n\n  const result = state.one(cleanTree, undefined)\n\n  if (!result) {\n    mdast = {type: 'root', children: []}\n  } else if (Array.isArray(result)) {\n    // Assume content.\n    const children = /** @type {Array<MdastRootContent>} */ (result)\n    mdast = {type: 'root', children}\n  } else {\n    mdast = result\n  }\n\n  // Collapse text nodes, and fix whitespace.\n  //\n  // Most of this is taken care of by `rehype-minify-whitespace`, but\n  // we’re generating some whitespace too, and some nodes are in the end\n  // ignored.\n  // So clean up.\n  (0,unist_util_visit__WEBPACK_IMPORTED_MODULE_3__.visit)(mdast, function (node, index, parent) {\n    if (node.type === 'text' && index !== undefined && parent) {\n      const previous = parent.children[index - 1]\n\n      if (previous && previous.type === node.type) {\n        previous.value += node.value\n        parent.children.splice(index, 1)\n\n        if (previous.position && node.position) {\n          previous.position.end = node.position.end\n        }\n\n        // Iterate over the previous node again, to handle its total value.\n        return index - 1\n      }\n\n      node.value = node.value.replace(/[\\t ]*(\\r?\\n|\\r)[\\t ]*/, '$1')\n\n      // We don’t care about other phrasing nodes in between (e.g., `[ asd ]()`),\n      // as there the whitespace matters.\n      if (\n        parent &&\n        (parent.type === 'heading' ||\n          parent.type === 'paragraph' ||\n          parent.type === 'root')\n      ) {\n        if (!index) {\n          node.value = node.value.replace(/^[\\t ]+/, '')\n        }\n\n        if (index === parent.children.length - 1) {\n          node.value = node.value.replace(/[\\t ]+$/, '')\n        }\n      }\n\n      if (!node.value) {\n        parent.children.splice(index, 1)\n        return index\n      }\n    }\n  })\n\n  return mdast\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-mdast/lib/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-mdast/lib/state.js":
/*!******************************************************!*\
  !*** ./node_modules/hast-util-to-mdast/lib/state.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createState: () => (/* binding */ createState)\n/* harmony export */ });\n/* harmony import */ var unist_util_position__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! unist-util-position */ \"(ssr)/./node_modules/unist-util-position/lib/index.js\");\n/* harmony import */ var _handlers_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./handlers/index.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/index.js\");\n/* harmony import */ var _util_wrap_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./util/wrap.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/util/wrap.js\");\n/**\n * @import {Element, Nodes, Parents} from 'hast'\n * @import {\n *   BlockContent as MdastBlockContent,\n *   DefinitionContent as MdastDefinitionContent,\n *   Nodes as MdastNodes,\n *   Parents as MdastParents,\n *   RootContent as MdastRootContent\n * } from 'mdast'\n */\n\n/**\n * @typedef {MdastBlockContent | MdastDefinitionContent} MdastFlowContent\n */\n\n/**\n * @callback All\n *   Transform the children of a hast parent to mdast.\n * @param {Parents} parent\n *   Parent.\n * @returns {Array<MdastRootContent>}\n *   mdast children.\n *\n * @callback Handle\n *   Handle a particular element.\n * @param {State} state\n *   Info passed around about the current state.\n * @param {Element} element\n *   Element to transform.\n * @param {Parents | undefined} parent\n *   Parent of `element`.\n * @returns {Array<MdastNodes> | MdastNodes | undefined | void}\n *   mdast node or nodes.\n *\n *   Note: `void` is included until TS nicely infers `undefined`.\n *\n * @callback NodeHandle\n *   Handle a particular node.\n * @param {State} state\n *   Info passed around about the current state.\n * @param {any} node\n *   Node to transform.\n * @param {Parents | undefined} parent\n *   Parent of `node`.\n * @returns {Array<MdastNodes> | MdastNodes | undefined | void}\n *   mdast node or nodes.\n *\n *   Note: `void` is included until TS nicely infers `undefined`.\n *\n * @callback One\n *   Transform a hast node to mdast.\n * @param {Nodes} node\n *   Expected hast node.\n * @param {Parents | undefined} parent\n *   Parent of `node`.\n * @returns {Array<MdastNodes> | MdastNodes | undefined}\n *   mdast result.\n *\n * @typedef Options\n *   Configuration.\n * @property {string | null | undefined} [checked='[x]']\n *   Value to use for a checked checkbox or radio input (default: `'[x]'`)\n * @property {boolean | null | undefined} [document]\n *   Whether the given tree represents a complete document (optional).\n *\n *   Applies when the `tree` is a `root` node.\n *   When the tree represents a complete document, then things are wrapped in\n *   paragraphs when needed, and otherwise they’re left as-is.\n *   The default checks for whether there’s mixed content: some phrasing nodes\n *   *and* some non-phrasing nodes.\n * @property {Record<string, Handle | null | undefined> | null | undefined} [handlers]\n *   Object mapping tag names to functions handling the corresponding elements\n *   (optional).\n *\n *   Merged into the defaults.\n * @property {boolean | null | undefined} [newlines=false]\n *   Keep line endings when collapsing whitespace (default: `false`).\n *\n *   The default collapses to a single space.\n * @property {Record<string, NodeHandle | null | undefined> | null | undefined} [nodeHandlers]\n *   Object mapping node types to functions handling the corresponding nodes\n *   (optional).\n *\n *   Merged into the defaults.\n * @property {Array<string> | null | undefined} [quotes=['\"']]\n *   List of quotes to use (default: `['\"']`).\n *\n *   Each value can be one or two characters.\n *   When two, the first character determines the opening quote and the second\n *   the closing quote at that level.\n *   When one, both the opening and closing quote are that character.\n *\n *   The order in which the preferred quotes appear determines which quotes to\n *   use at which level of nesting.\n *   So, to prefer `‘’` at the first level of nesting, and `“”` at the second,\n *   pass `['‘’', '“”']`.\n *   If `<q>`s are nested deeper than the given amount of quotes, the markers\n *   wrap around: a third level of nesting when using `['«»', '‹›']` should\n *   have double guillemets, a fourth single, a fifth double again, etc.\n * @property {string | null | undefined} [unchecked='[ ]']\n *   Value to use for an unchecked checkbox or radio input (default: `'[ ]'`).\n *\n * @callback Patch\n *   Copy a node’s positional info.\n * @param {Nodes} from\n *   hast node to copy from.\n * @param {MdastNodes} to\n *   mdast node to copy into.\n * @returns {undefined}\n *   Nothing.\n *\n * @callback Resolve\n *   Resolve a URL relative to a base.\n * @param {string | null | undefined} url\n *   Possible URL value.\n * @returns {string}\n *   URL, resolved to a `base` element, if any.\n *\n * @typedef State\n *   Info passed around about the current state.\n * @property {All} all\n *   Transform the children of a hast parent to mdast.\n * @property {boolean} baseFound\n *   Whether a `<base>` element was seen.\n * @property {Map<string, Element>} elementById\n *   Elements by their `id`.\n * @property {string | undefined} frozenBaseUrl\n *   `href` of `<base>`, if any.\n * @property {Record<string, Handle>} handlers\n *   Applied element handlers.\n * @property {boolean} inTable\n *   Whether we’re in a table.\n * @property {Record<string, NodeHandle>} nodeHandlers\n *   Applied node handlers.\n * @property {One} one\n *   Transform a hast node to mdast.\n * @property {Options} options\n *   User configuration.\n * @property {Patch} patch\n *   Copy a node’s positional info.\n * @property {number} qNesting\n *   Non-negative finite integer representing how deep we’re in `<q>`s.\n * @property {Resolve} resolve\n *   Resolve a URL relative to a base.\n * @property {ToFlow} toFlow\n *   Transform a list of mdast nodes to flow.\n * @property {<ChildType extends MdastNodes, ParentType extends MdastParents & {'children': Array<ChildType>}>(nodes: Array<MdastRootContent>, build: (() => ParentType)) => Array<ParentType>} toSpecificContent\n *   Turn arbitrary content into a list of a particular node type.\n *\n *   This is useful for example for lists, which must have list items as\n *   content.\n *   in this example, when non-items are found, they will be queued, and\n *   inserted into an adjacent item.\n *   When no actual items exist, one will be made with `build`.\n *\n * @callback ToFlow\n *   Transform a list of mdast nodes to flow.\n * @param {Array<MdastRootContent>} nodes\n *   mdast nodes.\n * @returns {Array<MdastFlowContent>}\n *   mdast flow children.\n */\n\n\n\n\n\nconst own = {}.hasOwnProperty\n\n/**\n * Create a state.\n *\n * @param {Readonly<Options>} options\n *   User configuration.\n * @returns {State}\n *   State.\n */\nfunction createState(options) {\n  return {\n    all,\n    baseFound: false,\n    elementById: new Map(),\n    frozenBaseUrl: undefined,\n    handlers: {..._handlers_index_js__WEBPACK_IMPORTED_MODULE_0__.handlers, ...options.handlers},\n    inTable: false,\n    nodeHandlers: {..._handlers_index_js__WEBPACK_IMPORTED_MODULE_0__.nodeHandlers, ...options.nodeHandlers},\n    one,\n    options,\n    patch,\n    qNesting: 0,\n    resolve,\n    toFlow,\n    toSpecificContent\n  }\n}\n\n/**\n * Transform the children of a hast parent to mdast.\n *\n * You might want to combine this with `toFlow` or `toSpecificContent`.\n *\n * @this {State}\n *   Info passed around about the current state.\n * @param {Parents} parent\n *   Parent.\n * @returns {Array<MdastRootContent>}\n *   mdast children.\n */\nfunction all(parent) {\n  const children = parent.children || []\n  /** @type {Array<MdastRootContent>} */\n  const results = []\n  let index = -1\n\n  while (++index < children.length) {\n    const child = children[index]\n    // Content -> content.\n    const result =\n      /** @type {Array<MdastRootContent> | MdastRootContent | undefined} */ (\n        this.one(child, parent)\n      )\n\n    if (Array.isArray(result)) {\n      results.push(...result)\n    } else if (result) {\n      results.push(result)\n    }\n  }\n\n  return results\n}\n\n/**\n * Transform a hast node to mdast.\n *\n * @this {State}\n *   Info passed around about the current state.\n * @param {Nodes} node\n *   hast node to transform.\n * @param {Parents | undefined} parent\n *   Parent of `node`.\n * @returns {Array<MdastNodes> | MdastNodes | undefined}\n *   mdast result.\n */\nfunction one(node, parent) {\n  if (node.type === 'element') {\n    if (node.properties && node.properties.dataMdast === 'ignore') {\n      return\n    }\n\n    if (own.call(this.handlers, node.tagName)) {\n      return this.handlers[node.tagName](this, node, parent) || undefined\n    }\n  } else if (own.call(this.nodeHandlers, node.type)) {\n    return this.nodeHandlers[node.type](this, node, parent) || undefined\n  }\n\n  // Unknown literal.\n  if ('value' in node && typeof node.value === 'string') {\n    /** @type {MdastRootContent} */\n    const result = {type: 'text', value: node.value}\n    this.patch(node, result)\n    return result\n  }\n\n  // Unknown parent.\n  if ('children' in node) {\n    return this.all(node)\n  }\n}\n\n/**\n * Copy a node’s positional info.\n *\n * @param {Nodes} origin\n *   hast node to copy from.\n * @param {MdastNodes} node\n *   mdast node to copy into.\n * @returns {undefined}\n *   Nothing.\n */\nfunction patch(origin, node) {\n  if (origin.position) node.position = (0,unist_util_position__WEBPACK_IMPORTED_MODULE_1__.position)(origin)\n}\n\n/**\n * @this {State}\n *   Info passed around about the current state.\n * @param {string | null | undefined} url\n *   Possible URL value.\n * @returns {string}\n *   URL, resolved to a `base` element, if any.\n */\nfunction resolve(url) {\n  const base = this.frozenBaseUrl\n\n  if (url === null || url === undefined) {\n    return ''\n  }\n\n  if (base) {\n    return String(new URL(url, base))\n  }\n\n  return url\n}\n\n/**\n * Transform a list of mdast nodes to flow.\n *\n * @this {State}\n *   Info passed around about the current state.\n * @param {Array<MdastRootContent>} nodes\n *   Parent.\n * @returns {Array<MdastFlowContent>}\n *   mdast flow children.\n */\nfunction toFlow(nodes) {\n  return (0,_util_wrap_js__WEBPACK_IMPORTED_MODULE_2__.wrap)(nodes)\n}\n\n/**\n * Turn arbitrary content into a particular node type.\n *\n * This is useful for example for lists, which must have list items as content.\n * in this example, when non-items are found, they will be queued, and\n * inserted into an adjacent item.\n * When no actual items exist, one will be made with `build`.\n *\n * @template {MdastNodes} ChildType\n *   Node type of children.\n * @template {MdastParents & {'children': Array<ChildType>}} ParentType\n *   Node type of parent.\n * @param {Array<MdastRootContent>} nodes\n *   Nodes, which are either `ParentType`, or will be wrapped in one.\n * @param {() => ParentType} build\n *   Build a parent if needed (must have empty `children`).\n * @returns {Array<ParentType>}\n *   List of parents.\n */\nfunction toSpecificContent(nodes, build) {\n  const reference = build()\n  /** @type {Array<ParentType>} */\n  const results = []\n  /** @type {Array<ChildType>} */\n  let queue = []\n  let index = -1\n\n  while (++index < nodes.length) {\n    const node = nodes[index]\n\n    if (expectedParent(node)) {\n      if (queue.length > 0) {\n        node.children.unshift(...queue)\n        queue = []\n      }\n\n      results.push(node)\n    } else {\n      // Assume `node` can be a child of `ParentType`.\n      // If we start checking nodes, we’d run into problems with unknown nodes,\n      // which we do want to support.\n      const child = /** @type {ChildType} */ (node)\n      queue.push(child)\n    }\n  }\n\n  if (queue.length > 0) {\n    let node = results[results.length - 1]\n\n    if (!node) {\n      node = build()\n      results.push(node)\n    }\n\n    node.children.push(...queue)\n    queue = []\n  }\n\n  return results\n\n  /**\n   * @param {MdastNodes} node\n   * @returns {node is ParentType}\n   */\n  function expectedParent(node) {\n    return node.type === reference.type\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-mdast/lib/state.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-mdast/lib/util/drop-surrounding-breaks.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/hast-util-to-mdast/lib/util/drop-surrounding-breaks.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   dropSurroundingBreaks: () => (/* binding */ dropSurroundingBreaks)\n/* harmony export */ });\n/**\n * @import {Nodes} from 'mdast'\n */\n\n/**\n * Drop trailing initial and final `br`s.\n *\n * @template {Nodes} Node\n *   Node type.\n * @param {Array<Node>} nodes\n *   List of nodes.\n * @returns {Array<Node>}\n *   List of nodes w/o `break`s.\n */\nfunction dropSurroundingBreaks(nodes) {\n  let start = 0\n  let end = nodes.length\n\n  while (start < end && nodes[start].type === 'break') start++\n  while (end > start && nodes[end - 1].type === 'break') end--\n\n  return start === 0 && end === nodes.length ? nodes : nodes.slice(start, end)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L2xpYi91dGlsL2Ryb3Atc3Vycm91bmRpbmctYnJlYWtzLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBLFlBQVksT0FBTztBQUNuQjs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxjQUFjLE9BQU87QUFDckI7QUFDQSxXQUFXLGFBQWE7QUFDeEI7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNPO0FBQ1A7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYmVuamlcXERlc2t0b3BcXEdfUFJPR1xcTmljb2xhc1xcYnRkYXNoLWVjb3N5c3RlbVxcYnRkYXNoc1xcbm9kZV9tb2R1bGVzXFxoYXN0LXV0aWwtdG8tbWRhc3RcXGxpYlxcdXRpbFxcZHJvcC1zdXJyb3VuZGluZy1icmVha3MuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAaW1wb3J0IHtOb2Rlc30gZnJvbSAnbWRhc3QnXG4gKi9cblxuLyoqXG4gKiBEcm9wIHRyYWlsaW5nIGluaXRpYWwgYW5kIGZpbmFsIGBicmBzLlxuICpcbiAqIEB0ZW1wbGF0ZSB7Tm9kZXN9IE5vZGVcbiAqICAgTm9kZSB0eXBlLlxuICogQHBhcmFtIHtBcnJheTxOb2RlPn0gbm9kZXNcbiAqICAgTGlzdCBvZiBub2Rlcy5cbiAqIEByZXR1cm5zIHtBcnJheTxOb2RlPn1cbiAqICAgTGlzdCBvZiBub2RlcyB3L28gYGJyZWFrYHMuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBkcm9wU3Vycm91bmRpbmdCcmVha3Mobm9kZXMpIHtcbiAgbGV0IHN0YXJ0ID0gMFxuICBsZXQgZW5kID0gbm9kZXMubGVuZ3RoXG5cbiAgd2hpbGUgKHN0YXJ0IDwgZW5kICYmIG5vZGVzW3N0YXJ0XS50eXBlID09PSAnYnJlYWsnKSBzdGFydCsrXG4gIHdoaWxlIChlbmQgPiBzdGFydCAmJiBub2Rlc1tlbmQgLSAxXS50eXBlID09PSAnYnJlYWsnKSBlbmQtLVxuXG4gIHJldHVybiBzdGFydCA9PT0gMCAmJiBlbmQgPT09IG5vZGVzLmxlbmd0aCA/IG5vZGVzIDogbm9kZXMuc2xpY2Uoc3RhcnQsIGVuZClcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-mdast/lib/util/drop-surrounding-breaks.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-mdast/lib/util/find-selected-options.js":
/*!***************************************************************************!*\
  !*** ./node_modules/hast-util-to-mdast/lib/util/find-selected-options.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   findSelectedOptions: () => (/* binding */ findSelectedOptions)\n/* harmony export */ });\n/* harmony import */ var hast_util_to_text__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! hast-util-to-text */ \"(ssr)/./node_modules/hast-util-to-text/lib/index.js\");\n/**\n * @import {Element, Properties} from 'hast'\n */\n\n/**\n * @typedef {[string, Value]} Option\n *   Option, where the item at `0` is the label, the item at `1` the value.\n *\n * @typedef {Array<Option>} Options\n *   List of options.\n *\n * @typedef {string | undefined} Value\n *   `value` field of option.\n */\n\n\n\n/**\n * @param {Readonly<Element>} node\n *   hast element to inspect.\n * @param {Properties | undefined} [explicitProperties]\n *   Properties to use, normally taken from `node`, but can be changed.\n * @returns {Options}\n *   Options.\n */\nfunction findSelectedOptions(node, explicitProperties) {\n  /** @type {Array<Element>} */\n  const selectedOptions = []\n  /** @type {Options} */\n  const values = []\n  const properties = explicitProperties || node.properties || {}\n  const options = findOptions(node)\n  const size =\n    Math.min(Number.parseInt(String(properties.size), 10), 0) ||\n    (properties.multiple ? 4 : 1)\n  let index = -1\n\n  while (++index < options.length) {\n    const option = options[index]\n\n    if (option && option.properties && option.properties.selected) {\n      selectedOptions.push(option)\n    }\n  }\n\n  const list = selectedOptions.length > 0 ? selectedOptions : options\n  const max = Math.min(list.length, size)\n  index = -1\n\n  while (++index < max) {\n    const option = list[index]\n    const properties = option.properties || {}\n    const content = (0,hast_util_to_text__WEBPACK_IMPORTED_MODULE_0__.toText)(option)\n    const label = content || String(properties.label || '')\n    const value = String(properties.value || '') || content\n    values.push([value, label === value ? undefined : label])\n  }\n\n  return values\n}\n\n/**\n * @param {Element} node\n *   Parent to find in.\n * @returns {Array<Element>}\n *   Option elements.\n */\nfunction findOptions(node) {\n  /** @type {Array<Element>} */\n  const results = []\n  let index = -1\n\n  while (++index < node.children.length) {\n    const child = node.children[index]\n\n    if ('children' in child && Array.isArray(child.children)) {\n      results.push(...findOptions(child))\n    }\n\n    if (\n      child.type === 'element' &&\n      child.tagName === 'option' &&\n      (!child.properties || !child.properties.disabled)\n    ) {\n      results.push(child)\n    }\n  }\n\n  return results\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-mdast/lib/util/find-selected-options.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-mdast/lib/util/list-items-spread.js":
/*!***********************************************************************!*\
  !*** ./node_modules/hast-util-to-mdast/lib/util/list-items-spread.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   listItemsSpread: () => (/* binding */ listItemsSpread)\n/* harmony export */ });\n/**\n * @import {ListContent} from 'mdast'\n */\n\n/**\n * Infer whether list items are spread.\n *\n * @param {Readonly<Array<Readonly<ListContent>>>} children\n *   List items.\n * @returns {boolean}\n *   Whether one or more list items are spread.\n */\nfunction listItemsSpread(children) {\n  let index = -1\n\n  if (children.length > 1) {\n    while (++index < children.length) {\n      if (children[index].spread) {\n        return true\n      }\n    }\n  }\n\n  return false\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLW1kYXN0L2xpYi91dGlsL2xpc3QtaXRlbXMtc3ByZWFkLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBLFlBQVksYUFBYTtBQUN6Qjs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLHdDQUF3QztBQUNuRDtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ087QUFDUDs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGJlbmppXFxEZXNrdG9wXFxHX1BST0dcXE5pY29sYXNcXGJ0ZGFzaC1lY29zeXN0ZW1cXGJ0ZGFzaHNcXG5vZGVfbW9kdWxlc1xcaGFzdC11dGlsLXRvLW1kYXN0XFxsaWJcXHV0aWxcXGxpc3QtaXRlbXMtc3ByZWFkLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGltcG9ydCB7TGlzdENvbnRlbnR9IGZyb20gJ21kYXN0J1xuICovXG5cbi8qKlxuICogSW5mZXIgd2hldGhlciBsaXN0IGl0ZW1zIGFyZSBzcHJlYWQuXG4gKlxuICogQHBhcmFtIHtSZWFkb25seTxBcnJheTxSZWFkb25seTxMaXN0Q29udGVudD4+Pn0gY2hpbGRyZW5cbiAqICAgTGlzdCBpdGVtcy5cbiAqIEByZXR1cm5zIHtib29sZWFufVxuICogICBXaGV0aGVyIG9uZSBvciBtb3JlIGxpc3QgaXRlbXMgYXJlIHNwcmVhZC5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGxpc3RJdGVtc1NwcmVhZChjaGlsZHJlbikge1xuICBsZXQgaW5kZXggPSAtMVxuXG4gIGlmIChjaGlsZHJlbi5sZW5ndGggPiAxKSB7XG4gICAgd2hpbGUgKCsraW5kZXggPCBjaGlsZHJlbi5sZW5ndGgpIHtcbiAgICAgIGlmIChjaGlsZHJlbltpbmRleF0uc3ByZWFkKSB7XG4gICAgICAgIHJldHVybiB0cnVlXG4gICAgICB9XG4gICAgfVxuICB9XG5cbiAgcmV0dXJuIGZhbHNlXG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-mdast/lib/util/list-items-spread.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-mdast/lib/util/wrap.js":
/*!**********************************************************!*\
  !*** ./node_modules/hast-util-to-mdast/lib/util/wrap.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   wrap: () => (/* binding */ wrap),\n/* harmony export */   wrapNeeded: () => (/* binding */ wrapNeeded)\n/* harmony export */ });\n/* harmony import */ var _ungap_structured_clone__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @ungap/structured-clone */ \"(ssr)/./node_modules/@ungap/structured-clone/esm/index.js\");\n/* harmony import */ var hast_util_phrasing__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! hast-util-phrasing */ \"(ssr)/./node_modules/hast-util-phrasing/lib/index.js\");\n/* harmony import */ var hast_util_whitespace__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! hast-util-whitespace */ \"(ssr)/./node_modules/hast-util-whitespace/lib/index.js\");\n/* harmony import */ var mdast_util_phrasing__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! mdast-util-phrasing */ \"(ssr)/./node_modules/mdast-util-phrasing/lib/index.js\");\n/* harmony import */ var _drop_surrounding_breaks_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./drop-surrounding-breaks.js */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/util/drop-surrounding-breaks.js\");\n/**\n * @import {} from 'mdast-util-to-hast'\n * @import {\n *   BlockContent,\n *   Delete,\n *   Link,\n *   Nodes,\n *   Paragraph,\n *   Parents,\n *   PhrasingContent,\n *   RootContent\n * } from 'mdast'\n */\n\n\n\n\n\n\n\n/**\n * Check if there are phrasing mdast nodes.\n *\n * This is needed if a fragment is given, which could just be a sentence, and\n * doesn’t need a wrapper paragraph.\n *\n * @param {Array<Nodes>} nodes\n * @returns {boolean}\n */\nfunction wrapNeeded(nodes) {\n  let index = -1\n\n  while (++index < nodes.length) {\n    const node = nodes[index]\n\n    if (!phrasing(node) || ('children' in node && wrapNeeded(node.children))) {\n      return true\n    }\n  }\n\n  return false\n}\n\n/**\n * Wrap runs of phrasing content into paragraphs, leaving the non-phrasing\n * content as-is.\n *\n * @param {Array<RootContent>} nodes\n *   Content.\n * @returns {Array<BlockContent>}\n *   Content where phrasing is wrapped in paragraphs.\n */\nfunction wrap(nodes) {\n  return runs(nodes, onphrasing, function (d) {\n    return d\n  })\n\n  /**\n   * @param {Array<PhrasingContent>} nodes\n   * @returns {Array<Paragraph>}\n   */\n  function onphrasing(nodes) {\n    return nodes.every(function (d) {\n      return d.type === 'text' ? (0,hast_util_whitespace__WEBPACK_IMPORTED_MODULE_0__.whitespace)(d.value) : false\n    })\n      ? []\n      : [{type: 'paragraph', children: (0,_drop_surrounding_breaks_js__WEBPACK_IMPORTED_MODULE_1__.dropSurroundingBreaks)(nodes)}]\n  }\n}\n\n/**\n * @param {Delete | Link} node\n * @returns {Array<BlockContent>}\n */\nfunction split(node) {\n  return runs(node.children, onphrasing, onnonphrasing)\n\n  /**\n   * Use `parent`, put the phrasing run inside it.\n   *\n   * @param {Array<PhrasingContent>} nodes\n   * @returns {Array<BlockContent>}\n   */\n  function onphrasing(nodes) {\n    const newParent = cloneWithoutChildren(node)\n    newParent.children = nodes\n    // @ts-expect-error Assume fine.\n    return [newParent]\n  }\n\n  /**\n   * Use `child`, add `parent` as its first child, put the original children\n   * into `parent`.\n   * If `child` is not a parent, `parent` will not be added.\n   *\n   * @param {BlockContent} child\n   * @returns {BlockContent}\n   */\n  function onnonphrasing(child) {\n    if ('children' in child && 'children' in node) {\n      const newParent = cloneWithoutChildren(node)\n      const newChild = cloneWithoutChildren(child)\n      // @ts-expect-error Assume fine.\n      newParent.children = child.children\n      // @ts-expect-error Assume fine.\n      newChild.children.push(newParent)\n      return newChild\n    }\n\n    return {...child}\n  }\n}\n\n/**\n * Wrap all runs of mdast phrasing content in `paragraph` nodes.\n *\n * @param {Array<RootContent>} nodes\n *   List of input nodes.\n * @param {(nodes: Array<PhrasingContent>) => Array<BlockContent>} onphrasing\n *   Turn phrasing content into block content.\n * @param {(node: BlockContent) => BlockContent} onnonphrasing\n *   Map block content (defaults to keeping them as-is).\n * @returns {Array<BlockContent>}\n */\nfunction runs(nodes, onphrasing, onnonphrasing) {\n  const flattened = flatten(nodes)\n  /** @type {Array<BlockContent>} */\n  const result = []\n  /** @type {Array<PhrasingContent>} */\n  let queue = []\n  let index = -1\n\n  while (++index < flattened.length) {\n    const node = flattened[index]\n\n    if (phrasing(node)) {\n      queue.push(node)\n    } else {\n      if (queue.length > 0) {\n        result.push(...onphrasing(queue))\n        queue = []\n      }\n\n      // @ts-expect-error Assume non-phrasing.\n      result.push(onnonphrasing(node))\n    }\n  }\n\n  if (queue.length > 0) {\n    result.push(...onphrasing(queue))\n    queue = []\n  }\n\n  return result\n}\n\n/**\n * Flatten a list of nodes.\n *\n * @param {Array<RootContent>} nodes\n *   List of nodes, will unravel `delete` and `link`.\n * @returns {Array<RootContent>}\n *   Unraveled nodes.\n */\nfunction flatten(nodes) {\n  /** @type {Array<RootContent>} */\n  const flattened = []\n  let index = -1\n\n  while (++index < nodes.length) {\n    const node = nodes[index]\n\n    // Straddling: some elements are *weird*.\n    // Namely: `map`, `ins`, `del`, and `a`, as they are hybrid elements.\n    // See: <https://html.spec.whatwg.org/#paragraphs>.\n    // Paragraphs are the weirdest of them all.\n    // See the straddling fixture for more info!\n    // `ins` is ignored in mdast, so we don’t need to worry about that.\n    // `map` maps to its content, so we don’t need to worry about that either.\n    // `del` maps to `delete` and `a` to `link`, so we do handle those.\n    // What we’ll do is split `node` over each of its children.\n    if (\n      (node.type === 'delete' || node.type === 'link') &&\n      wrapNeeded(node.children)\n    ) {\n      flattened.push(...split(node))\n    } else {\n      flattened.push(node)\n    }\n  }\n\n  return flattened\n}\n\n/**\n * Check if an mdast node is phrasing.\n *\n * Also supports checking embedded hast fields.\n *\n * @param {Nodes} node\n *   mdast node to check.\n * @returns {node is PhrasingContent}\n *   Whether `node` is phrasing content (includes nodes with `hName` fields\n *   set to phrasing hast element names).\n */\nfunction phrasing(node) {\n  const tagName = node.data && node.data.hName\n  return tagName\n    ? (0,hast_util_phrasing__WEBPACK_IMPORTED_MODULE_2__.phrasing)({type: 'element', tagName, properties: {}, children: []})\n    : (0,mdast_util_phrasing__WEBPACK_IMPORTED_MODULE_3__.phrasing)(node)\n}\n\n/**\n * @template {Parents} ParentType\n *   Parent type.\n * @param {ParentType} node\n *   Node to clone.\n * @returns {ParentType}\n *   Cloned node, without children.\n */\nfunction cloneWithoutChildren(node) {\n  return (0,_ungap_structured_clone__WEBPACK_IMPORTED_MODULE_4__[\"default\"])({...node, children: []})\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-mdast/lib/util/wrap.js\n");

/***/ })

};
;