"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/hast-util-is-body-ok-link";
exports.ids = ["vendor-chunks/hast-util-is-body-ok-link"];
exports.modules = {

/***/ "(ssr)/./node_modules/hast-util-is-body-ok-link/lib/index.js":
/*!*************************************************************!*\
  !*** ./node_modules/hast-util-is-body-ok-link/lib/index.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isBodyOkLink: () => (/* binding */ isBodyOkLink)\n/* harmony export */ });\n/**\n * @import {Nodes} from 'hast'\n */\n\nconst list = new Set(['pingback', 'prefetch', 'stylesheet'])\n\n/**\n * Checks whether a node is a “body OK” link.\n *\n * @param {Nodes} node\n *   Node to check.\n * @returns {boolean}\n *   Whether `node` is a “body OK” link.\n */\nfunction isBodyOkLink(node) {\n  if (node.type !== 'element' || node.tagName !== 'link') {\n    return false\n  }\n\n  if (node.properties.itemProp) {\n    return true\n  }\n\n  const value = node.properties.rel\n  let index = -1\n\n  if (!Array.isArray(value) || value.length === 0) {\n    return false\n  }\n\n  while (++index < value.length) {\n    if (!list.has(String(value[index]))) {\n      return false\n    }\n  }\n\n  return true\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLWlzLWJvZHktb2stbGluay9saWIvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0EsWUFBWSxPQUFPO0FBQ25COztBQUVBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsT0FBTztBQUNsQjtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxiZW5qaVxcRGVza3RvcFxcR19QUk9HXFxOaWNvbGFzXFxidGRhc2gtZWNvc3lzdGVtXFxidGRhc2hzXFxub2RlX21vZHVsZXNcXGhhc3QtdXRpbC1pcy1ib2R5LW9rLWxpbmtcXGxpYlxcaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAaW1wb3J0IHtOb2Rlc30gZnJvbSAnaGFzdCdcbiAqL1xuXG5jb25zdCBsaXN0ID0gbmV3IFNldChbJ3BpbmdiYWNrJywgJ3ByZWZldGNoJywgJ3N0eWxlc2hlZXQnXSlcblxuLyoqXG4gKiBDaGVja3Mgd2hldGhlciBhIG5vZGUgaXMgYSDigJxib2R5IE9L4oCdIGxpbmsuXG4gKlxuICogQHBhcmFtIHtOb2Rlc30gbm9kZVxuICogICBOb2RlIHRvIGNoZWNrLlxuICogQHJldHVybnMge2Jvb2xlYW59XG4gKiAgIFdoZXRoZXIgYG5vZGVgIGlzIGEg4oCcYm9keSBPS+KAnSBsaW5rLlxuICovXG5leHBvcnQgZnVuY3Rpb24gaXNCb2R5T2tMaW5rKG5vZGUpIHtcbiAgaWYgKG5vZGUudHlwZSAhPT0gJ2VsZW1lbnQnIHx8IG5vZGUudGFnTmFtZSAhPT0gJ2xpbmsnKSB7XG4gICAgcmV0dXJuIGZhbHNlXG4gIH1cblxuICBpZiAobm9kZS5wcm9wZXJ0aWVzLml0ZW1Qcm9wKSB7XG4gICAgcmV0dXJuIHRydWVcbiAgfVxuXG4gIGNvbnN0IHZhbHVlID0gbm9kZS5wcm9wZXJ0aWVzLnJlbFxuICBsZXQgaW5kZXggPSAtMVxuXG4gIGlmICghQXJyYXkuaXNBcnJheSh2YWx1ZSkgfHwgdmFsdWUubGVuZ3RoID09PSAwKSB7XG4gICAgcmV0dXJuIGZhbHNlXG4gIH1cblxuICB3aGlsZSAoKytpbmRleCA8IHZhbHVlLmxlbmd0aCkge1xuICAgIGlmICghbGlzdC5oYXMoU3RyaW5nKHZhbHVlW2luZGV4XSkpKSB7XG4gICAgICByZXR1cm4gZmFsc2VcbiAgICB9XG4gIH1cblxuICByZXR1cm4gdHJ1ZVxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-is-body-ok-link/lib/index.js\n");

/***/ })

};
;