# Subnet Verification Testing Guide

## Overview

This guide provides comprehensive testing instructions for the subnet ownership verification feature in BTDash. It covers environment setup, UI testing workflows, and various test scenarios.

## Quick Start

For the fastest testing setup:

1. **Install Bittensor Wallet Extension** from [Chrome Web Store](https://chromewebstore.google.com/detail/bittensor-wallet/bdgmdoedahdcjmpmifafdhnffjinddgc)
2. **Create or import** your Bittensor wallet in the extension
3. **Connect to testnet** (the extension should default to testnet)
4. **Get test TAO** from the faucet if needed
5. **Start BTDash** development environment
6. **Begin testing** using the workflows below

> **Note**: We use the public Bittensor testnet for all testing to simplify setup. No local blockchain required!

## 1. Test Environment Setup

### 1.1 Bittensor Testnet Setup

We'll use the public Bittensor testnet for all testing to simplify the setup process.

#### Testnet Details

-   **Network**: Bittensor Testnet
-   **RPC Endpoint**: `wss://test.finney.opentensor.ai:443`
-   **Chain ID**: `test-finney`
-   **Explorer**: [Taostats Testnet](https://testnet.taostats.io/)

#### Prerequisites

-   Bittensor Wallet Extension installed
-   Test TAO tokens (from faucet)
-   BTDash development environment running

### 1.2 Test Subnet Setup

#### Option 1: Create Your Own Test Subnet (Recommended)

To properly test the verification feature, you need to own a subnet. Here's how to create one on testnet:

```bash
# Install bittensor
pip install bittensor

# Create a test wallet
btcli wallet new_coldkey --wallet.name test_wallet
btcli wallet new_hotkey --wallet.name test_wallet --wallet.hotkey test_hotkey

# Get test tokens from faucet
btcli wallet faucet --wallet.name test_wallet --subtensor.network test

# Register a subnet (requires 100 test TAO)
btcli subnet create --wallet.name test_wallet --subtensor.network test
```

### 1.3 Bittensor Wallet Extension Setup

#### Install Bittensor Wallet Extension

1. **Install from Chrome Web Store**:

    - Go to [Bittensor Wallet Extension](https://chromewebstore.google.com/detail/bittensor-wallet/bdgmdoedahdcjmpmifafdhnffjinddgc)
    - Click "Add to Chrome"
    - Pin the extension to your browser toolbar

2. **Create or Import Wallet**:
    - Click the Bittensor wallet icon in your browser
    - Choose "Create New Wallet" or "Import Existing Wallet"
    - Follow the setup wizard

#### Import Existing Bittensor Wallet

If you have an existing Bittensor wallet from CLI:

```bash
# Export your wallet's mnemonic
btcli wallet export_mnemonic --wallet.name test_wallet

# In the browser extension:
# 1. Click "Import Existing Wallet"
# 2. Enter your mnemonic phrase
# 3. Set a password for the extension
```

### 1.4 Database Setup

#### Add Test Data to Database

```sql
-- Add test subnet to database
INSERT INTO dtm_base.subnets (netuid, name, owner_address, description, image_url, created_at, updated_at)
VALUES (123, 'Test Verification Subnet', '5GrwvaEF5zXb26Fz9rcQpDWS57CtERHpNehXCPcNoHGKutQY', 'Test subnet for verification', NULL, NOW(), NOW());

-- Create test company
INSERT INTO dtm_base.companies (name, description, website, created_at, updated_at)
VALUES ('Test Company', 'Test company for subnet verification', 'https://test.com', NOW(), NOW());

-- Add user to company as owner
INSERT INTO dtm_base.user_company (user_id, company_id, role, created_at, updated_at)
VALUES (1, 1, 'owner', NOW(), NOW());
```

## 2. UI Testing Workflow

### 2.1 Accessing the Verification Interface

#### Step 1: Login and Navigate

1. Start the BTDash frontend: `npm run dev`
2. Login with your test account
3. Navigate to your company profile
4. Look for the "Verified Subnets" section
5. Click "Verify Subnet" button

#### Step 2: Initial Interface Check

**Expected UI Elements**:

-   Progress bar showing current step
-   Subnet selection dropdown
-   Clear instructions and descriptions
-   Error handling alerts (if any)

### 2.2 Step-by-Step Verification Process

#### Step 1: Select Subnet

1. **Action**: Open the subnet dropdown
2. **Expected**: List of available subnets appears
3. **Verify**:
    - Subnets show netuid, name, and owner address
    - Only unverified subnets appear in the list
    - Selected subnet details display correctly

#### Step 2: Connect Wallet

1. **Action**: Click "Connect Wallet"
2. **Expected**: Browser wallet extension opens
3. **Verify**:
    - Wallet connection prompt appears
    - Account selection is available
    - Connected account displays correctly
    - Account address matches subnet owner

#### Step 3: Generate Challenge

1. **Action**: Click "Generate Challenge"
2. **Expected**: Challenge message appears
3. **Verify**:
    - Challenge contains subnet details
    - Challenge contains company information
    - Challenge contains timestamp and nonce
    - Challenge expires in 15 minutes
    - Copy button works

#### Step 4: Sign Challenge

1. **Action**: Click "Sign with Wallet"
2. **Expected**: Wallet signing prompt opens
3. **Verify**:
    - Signing prompt shows the challenge message
    - User can review before signing
    - Signature is captured after approval

#### Step 5: Complete Verification

1. **Action**: System automatically submits signature
2. **Expected**: Verification completes
3. **Verify**:
    - Success message appears
    - Verification ID is displayed
    - Subnet appears in verified list
    - Database record is created

### 2.3 Post-Verification Checks

#### UI Verification

1. **Company Profile**: Verified subnet appears in the list
2. **Subnet Details**: Shows verification timestamp and ID
3. **Management Options**: Remove verification button available (for owners/admins)

#### Database Verification

```sql
-- Check verification record
SELECT * FROM dtm_base.verified_subnet_ownership
WHERE netuid = 123 AND company_id = 1;

-- Check company netuids array
SELECT netuids FROM dtm_base.companies WHERE id = 1;

-- Check verification attempts log
SELECT * FROM dtm_base.subnet_verification_attempts
WHERE netuid = 123 ORDER BY created_at DESC LIMIT 5;
```

## 3. Test Scenarios

### 3.1 Happy Path: Successful Verification

#### Test Case: Complete Verification Flow

```
Preconditions:
- User is company owner/admin
- Subnet exists and is owned by user's wallet
- Wallet extension is installed and configured

Steps:
1. Navigate to company profile
2. Click "Verify Subnet"
3. Select test subnet (netuid: 123)
4. Connect wallet with correct account
5. Generate challenge
6. Sign challenge with wallet
7. Complete verification

Expected Result:
- Verification succeeds
- Subnet appears in verified list
- Database records created
- Success notification shown
```

### 3.2 Error Cases

#### Test Case: Wrong Wallet Address

```
Preconditions:
- User connects wallet with different address than subnet owner

Steps:
1. Follow happy path until wallet connection
2. Connect wallet with wrong account
3. Attempt verification

Expected Result:
- Error: "The signing address is not the owner of this subnet"
- Verification fails
- User can retry with correct wallet
```

#### Test Case: Expired Challenge

```
Preconditions:
- Challenge is generated but not signed within 15 minutes

Steps:
1. Generate challenge
2. Wait 16 minutes
3. Attempt to sign expired challenge

Expected Result:
- Error: "Challenge has expired. Please generate a new challenge."
- User must generate new challenge
```

#### Test Case: Invalid Signature

```
Preconditions:
- Simulate invalid signature (requires code modification for testing)

Steps:
1. Generate challenge
2. Provide invalid signature
3. Submit verification

Expected Result:
- Error: "Invalid signature. Please check your wallet signature."
- Verification fails
```

#### Test Case: Insufficient Permissions

```
Preconditions:
- User is not company owner/admin

Steps:
1. Login as regular member
2. Attempt to access verification interface

Expected Result:
- Error: "Insufficient permissions to verify subnets for this company"
- Verification interface not accessible
```

### 3.3 Edge Cases

#### Test Case: Rate Limiting

```
Preconditions:
- Rate limiting is enabled

Steps:
1. Generate 6 challenges within 5 minutes
2. Attempt 7th challenge

Expected Result:
- Error: "Rate limit exceeded. Please try again later."
- User must wait before retrying
```

#### Test Case: Already Verified Subnet

```
Preconditions:
- Subnet is already verified for the company

Steps:
1. Attempt to verify same subnet again

Expected Result:
- Error: "Subnet is already verified for this company"
- Subnet doesn't appear in available list
```

#### Test Case: Network Connectivity Issues

```
Preconditions:
- Simulate network issues (disconnect internet)

Steps:
1. Attempt verification with network issues

Expected Result:
- Error: "Failed to generate challenge" or "Failed to verify ownership"
- Clear error message displayed
- User can retry when connection restored
```

## 4. API Testing

### 4.1 Manual API Testing

#### Test Challenge Generation

```bash
curl -X POST http://localhost:5000/api/subnet-verification/challenge \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "netuid": 123,
    "companyId": 1
  }'
```

#### Test Verification

```bash
curl -X POST http://localhost:5000/api/subnet-verification/verify \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "challengeId": 1,
    "signature": "0x...",
    "signerAddress": "5GrwvaEF5zXb26Fz9rcQpDWS57CtERHpNehXCPcNoHGKutQY",
    "companyId": 1
  }'
```

### 4.2 Automated Testing

#### Run Test Suite

```bash
cd btdashs_api
npm test -- --grep "subnet verification"
```

## 5. Security Testing

### 5.1 Authentication Testing

-   Test without JWT token
-   Test with expired token
-   Test with invalid token

### 5.2 Authorization Testing

-   Test with different user roles
-   Test cross-company access attempts

### 5.3 Input Validation Testing

-   Test with invalid netuid values
-   Test with malformed signatures
-   Test with SQL injection attempts

## 6. Performance Testing

### 6.1 Load Testing

```bash
# Install artillery for load testing
npm install -g artillery

# Create load test config
artillery quick --count 10 --num 5 http://localhost:5000/api/subnet-verification/challenge
```

### 6.2 Concurrent User Testing

-   Test multiple users verifying simultaneously
-   Test rate limiting under load
-   Monitor database performance

## 7. Troubleshooting Common Issues

### 7.1 Wallet Connection Issues

-   **Problem**: Wallet extension not detected
-   **Solution**: Refresh page, check extension installation
-   **Debug**: Check browser console for errors

### 7.2 Signature Verification Failures

-   **Problem**: Valid signature rejected
-   **Solution**: Check address format, verify subnet ownership
-   **Debug**: Check backend logs for detailed error messages

### 7.3 Database Connection Issues

-   **Problem**: Verification fails to save
-   **Solution**: Check database connection, verify table structure
-   **Debug**: Check database logs and API server logs

## 8. Test Data Cleanup

### 8.1 Reset Test Environment

```sql
-- Clean up test data
DELETE FROM dtm_base.verified_subnet_ownership WHERE company_id = 1;
DELETE FROM dtm_base.subnet_verification_attempts WHERE netuid = 123;
DELETE FROM dtm_base.subnet_verification_challenges WHERE netuid = 123;
```

### 8.2 Reset Wallet State

-   Clear browser extension data
-   Re-import test wallet
-   Reset local storage in browser

## 9. Quick Testing Checklist

### 9.1 Pre-Test Setup Checklist

-   [ ] Backend API running on port 5000
-   [ ] Frontend running on port 3000
-   [ ] Database contains test subnet data
-   [ ] Bittensor Wallet Extension installed and configured
-   [ ] Test wallet connected to Bittensor testnet
-   [ ] Test user has company owner/admin role
-   [ ] Test TAO tokens available (if creating subnets)

### 9.2 Happy Path Testing Checklist

-   [ ] Can access verification interface
-   [ ] Subnet dropdown shows available subnets
-   [ ] Wallet connects successfully
-   [ ] Challenge generates with correct data
-   [ ] Challenge can be copied to clipboard
-   [ ] Wallet signing prompt appears
-   [ ] Signature is captured correctly
-   [ ] Verification completes successfully
-   [ ] Success message displays
-   [ ] Verified subnet appears in list
-   [ ] Database record created correctly

### 9.3 Error Handling Checklist

-   [ ] Proper error for wrong wallet address
-   [ ] Proper error for expired challenge
-   [ ] Proper error for invalid signature
-   [ ] Proper error for insufficient permissions
-   [ ] Proper error for rate limiting
-   [ ] Proper error for network issues
-   [ ] Error messages are user-friendly
-   [ ] Errors don't expose sensitive information

### 9.4 UI/UX Checklist

-   [ ] Progress bar updates correctly
-   [ ] Loading states display properly
-   [ ] Buttons are disabled when appropriate
-   [ ] Form validation works correctly
-   [ ] Responsive design on mobile
-   [ ] Dark/light theme consistency
-   [ ] Accessibility features work
-   [ ] Copy buttons function correctly

## 10. Automated Testing Scripts

### 10.1 Run API Integration Tests

```bash
cd btdashs_api
npm test tests/subnet-verification-integration.test.js
```

### 10.2 Run Manual API Testing Script

```bash
cd btdashs_api
node scripts/test-subnet-verification.js
```

### 10.3 Frontend Component Testing

```bash
cd btdashs
npm test -- --testPathPattern=subnet-verification
```

This comprehensive testing guide ensures thorough validation of the subnet verification feature across all scenarios and edge cases.
