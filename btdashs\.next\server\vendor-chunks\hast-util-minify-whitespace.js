"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/hast-util-minify-whitespace";
exports.ids = ["vendor-chunks/hast-util-minify-whitespace"];
exports.modules = {

/***/ "(ssr)/./node_modules/hast-util-minify-whitespace/lib/block.js":
/*!***************************************************************!*\
  !*** ./node_modules/hast-util-minify-whitespace/lib/block.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   blocks: () => (/* binding */ blocks)\n/* harmony export */ });\n// See: <https://html.spec.whatwg.org/#the-css-user-agent-style-sheet-and-presentational-hints>\nconst blocks = [\n  'address', // Flow content.\n  'article', // Sections and headings.\n  'aside', // Sections and headings.\n  'blockquote', // Flow content.\n  'body', // Page.\n  'br', // Contribute whitespace intrinsically.\n  'caption', // Similar to block.\n  'center', // Flow content, legacy.\n  'col', // Similar to block.\n  'colgroup', // Similar to block.\n  'dd', // Lists.\n  'dialog', // Flow content.\n  'dir', // Lists, legacy.\n  'div', // Flow content.\n  'dl', // Lists.\n  'dt', // Lists.\n  'figcaption', // Flow content.\n  'figure', // Flow content.\n  'footer', // Flow content.\n  'form', // Flow content.\n  'h1', // Sections and headings.\n  'h2', // Sections and headings.\n  'h3', // Sections and headings.\n  'h4', // Sections and headings.\n  'h5', // Sections and headings.\n  'h6', // Sections and headings.\n  'head', // Page.\n  'header', // Flow content.\n  'hgroup', // Sections and headings.\n  'hr', // Flow content.\n  'html', // Page.\n  'legend', // Flow content.\n  'li', // Block-like.\n  'li', // Similar to block.\n  'listing', // Flow content, legacy\n  'main', // Flow content.\n  'menu', // Lists.\n  'nav', // Sections and headings.\n  'ol', // Lists.\n  'optgroup', // Similar to block.\n  'option', // Similar to block.\n  'p', // Flow content.\n  'plaintext', // Flow content, legacy\n  'pre', // Flow content.\n  'section', // Sections and headings.\n  'summary', // Similar to block.\n  'table', // Similar to block.\n  'tbody', // Similar to block.\n  'td', // Block-like.\n  'td', // Similar to block.\n  'tfoot', // Similar to block.\n  'th', // Block-like.\n  'th', // Similar to block.\n  'thead', // Similar to block.\n  'tr', // Similar to block.\n  'ul', // Lists.\n  'wbr', // Contribute whitespace intrinsically.\n  'xmp' // Flow content, legacy\n]\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-minify-whitespace/lib/block.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-minify-whitespace/lib/content.js":
/*!*****************************************************************!*\
  !*** ./node_modules/hast-util-minify-whitespace/lib/content.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   content: () => (/* binding */ content)\n/* harmony export */ });\nconst content = [\n  // Form.\n  'button',\n  'input',\n  'select',\n  'textarea'\n]\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLW1pbmlmeS13aGl0ZXNwYWNlL2xpYi9jb250ZW50LmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxiZW5qaVxcRGVza3RvcFxcR19QUk9HXFxOaWNvbGFzXFxidGRhc2gtZWNvc3lzdGVtXFxidGRhc2hzXFxub2RlX21vZHVsZXNcXGhhc3QtdXRpbC1taW5pZnktd2hpdGVzcGFjZVxcbGliXFxjb250ZW50LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCBjb250ZW50ID0gW1xuICAvLyBGb3JtLlxuICAnYnV0dG9uJyxcbiAgJ2lucHV0JyxcbiAgJ3NlbGVjdCcsXG4gICd0ZXh0YXJlYSdcbl1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-minify-whitespace/lib/content.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-minify-whitespace/lib/index.js":
/*!***************************************************************!*\
  !*** ./node_modules/hast-util-minify-whitespace/lib/index.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   minifyWhitespace: () => (/* binding */ minifyWhitespace)\n/* harmony export */ });\n/* harmony import */ var hast_util_embedded__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! hast-util-embedded */ \"(ssr)/./node_modules/hast-util-embedded/lib/index.js\");\n/* harmony import */ var hast_util_is_element__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! hast-util-is-element */ \"(ssr)/./node_modules/hast-util-is-element/lib/index.js\");\n/* harmony import */ var hast_util_whitespace__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! hast-util-whitespace */ \"(ssr)/./node_modules/hast-util-whitespace/lib/index.js\");\n/* harmony import */ var unist_util_is__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! unist-util-is */ \"(ssr)/./node_modules/unist-util-is/lib/index.js\");\n/* harmony import */ var _block_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./block.js */ \"(ssr)/./node_modules/hast-util-minify-whitespace/lib/block.js\");\n/* harmony import */ var _content_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./content.js */ \"(ssr)/./node_modules/hast-util-minify-whitespace/lib/content.js\");\n/* harmony import */ var _skippable_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./skippable.js */ \"(ssr)/./node_modules/hast-util-minify-whitespace/lib/skippable.js\");\n/**\n * @import {Nodes, Parents, Text} from 'hast'\n */\n\n/**\n * @callback Collapse\n *   Collapse a string.\n * @param {string} value\n *   Value to collapse.\n * @returns {string}\n *   Collapsed value.\n *\n * @typedef Options\n *   Configuration.\n * @property {boolean | null | undefined} [newlines=false]\n *   Collapse whitespace containing newlines to `'\\n'` instead of `' '`\n *   (default: `false`); the default is to collapse to a single space.\n *\n * @typedef Result\n *   Result.\n * @property {boolean} remove\n *   Whether to remove.\n * @property {boolean} ignore\n *   Whether to ignore.\n * @property {boolean} stripAtStart\n *   Whether to strip at the start.\n *\n * @typedef State\n *   Info passed around.\n * @property {Collapse} collapse\n *   Collapse.\n * @property {Whitespace} whitespace\n *   Current whitespace.\n * @property {boolean | undefined} [before]\n *   Whether there is a break before (default: `false`).\n * @property {boolean | undefined} [after]\n *   Whether there is a break after (default: `false`).\n *\n * @typedef {'normal' | 'nowrap' | 'pre' | 'pre-wrap'} Whitespace\n *   Whitespace setting.\n */\n\n\n\n\n\n\n\n\n\n/** @type {Options} */\nconst emptyOptions = {}\nconst ignorableNode = (0,unist_util_is__WEBPACK_IMPORTED_MODULE_0__.convert)(['comment', 'doctype'])\n\n/**\n * Minify whitespace.\n *\n * @param {Nodes} tree\n *   Tree.\n * @param {Options | null | undefined} [options]\n *   Configuration (optional).\n * @returns {undefined}\n *   Nothing.\n */\nfunction minifyWhitespace(tree, options) {\n  const settings = options || emptyOptions\n\n  minify(tree, {\n    collapse: collapseFactory(\n      settings.newlines ? replaceNewlines : replaceWhitespace\n    ),\n    whitespace: 'normal'\n  })\n}\n\n/**\n * @param {Nodes} node\n *   Node.\n * @param {State} state\n *   Info passed around.\n * @returns {Result}\n *   Result.\n */\nfunction minify(node, state) {\n  if ('children' in node) {\n    const settings = {...state}\n\n    if (node.type === 'root' || blocklike(node)) {\n      settings.before = true\n      settings.after = true\n    }\n\n    settings.whitespace = inferWhiteSpace(node, state)\n\n    return all(node, settings)\n  }\n\n  if (node.type === 'text') {\n    if (state.whitespace === 'normal') {\n      return minifyText(node, state)\n    }\n\n    // Naïve collapse, but no trimming:\n    if (state.whitespace === 'nowrap') {\n      node.value = state.collapse(node.value)\n    }\n\n    // The `pre-wrap` or `pre` whitespace settings are neither collapsed nor\n    // trimmed.\n  }\n\n  return {ignore: ignorableNode(node), stripAtStart: false, remove: false}\n}\n\n/**\n * @param {Text} node\n *   Node.\n * @param {State} state\n *   Info passed around.\n * @returns {Result}\n *   Result.\n */\nfunction minifyText(node, state) {\n  const value = state.collapse(node.value)\n  const result = {ignore: false, stripAtStart: false, remove: false}\n  let start = 0\n  let end = value.length\n\n  if (state.before && removable(value.charAt(0))) {\n    start++\n  }\n\n  if (start !== end && removable(value.charAt(end - 1))) {\n    if (state.after) {\n      end--\n    } else {\n      result.stripAtStart = true\n    }\n  }\n\n  if (start === end) {\n    result.remove = true\n  } else {\n    node.value = value.slice(start, end)\n  }\n\n  return result\n}\n\n/**\n * @param {Parents} parent\n *   Node.\n * @param {State} state\n *   Info passed around.\n * @returns {Result}\n *   Result.\n */\nfunction all(parent, state) {\n  let before = state.before\n  const after = state.after\n  const children = parent.children\n  let length = children.length\n  let index = -1\n\n  while (++index < length) {\n    const result = minify(children[index], {\n      ...state,\n      after: collapsableAfter(children, index, after),\n      before\n    })\n\n    if (result.remove) {\n      children.splice(index, 1)\n      index--\n      length--\n    } else if (!result.ignore) {\n      before = result.stripAtStart\n    }\n\n    // If this element, such as a `<select>` or `<img>`, contributes content\n    // somehow, allow whitespace again.\n    if (content(children[index])) {\n      before = false\n    }\n  }\n\n  return {ignore: false, stripAtStart: Boolean(before || after), remove: false}\n}\n\n/**\n * @param {Array<Nodes>} nodes\n *   Nodes.\n * @param {number} index\n *   Index.\n * @param {boolean | undefined} [after]\n *   Whether there is a break after `nodes` (default: `false`).\n * @returns {boolean | undefined}\n *   Whether there is a break after the node at `index`.\n */\nfunction collapsableAfter(nodes, index, after) {\n  while (++index < nodes.length) {\n    const node = nodes[index]\n    let result = inferBoundary(node)\n\n    if (result === undefined && 'children' in node && !skippable(node)) {\n      result = collapsableAfter(node.children, -1)\n    }\n\n    if (typeof result === 'boolean') {\n      return result\n    }\n  }\n\n  return after\n}\n\n/**\n * Infer two types of boundaries:\n *\n * 1. `true` — boundary for which whitespace around it does not contribute\n *    anything\n * 2. `false` — boundary for which whitespace around it *does* contribute\n *\n * No result (`undefined`) is returned if it is unknown.\n *\n * @param {Nodes} node\n *   Node.\n * @returns {boolean | undefined}\n *   Boundary.\n */\nfunction inferBoundary(node) {\n  if (node.type === 'element') {\n    if (content(node)) {\n      return false\n    }\n\n    if (blocklike(node)) {\n      return true\n    }\n\n    // Unknown: either depends on siblings if embedded or metadata, or on\n    // children.\n  } else if (node.type === 'text') {\n    if (!(0,hast_util_whitespace__WEBPACK_IMPORTED_MODULE_1__.whitespace)(node)) {\n      return false\n    }\n  } else if (!ignorableNode(node)) {\n    return false\n  }\n}\n\n/**\n * Infer whether a node is skippable.\n *\n * @param {Nodes} node\n *   Node.\n * @returns {boolean}\n *   Whether `node` is skippable.\n */\nfunction content(node) {\n  return (0,hast_util_embedded__WEBPACK_IMPORTED_MODULE_2__.embedded)(node) || (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_3__.isElement)(node, _content_js__WEBPACK_IMPORTED_MODULE_4__.content)\n}\n\n/**\n * See: <https://html.spec.whatwg.org/#the-css-user-agent-style-sheet-and-presentational-hints>\n *\n * @param {Nodes} node\n *   Node.\n * @returns {boolean}\n *   Whether `node` is block-like.\n */\nfunction blocklike(node) {\n  return (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_3__.isElement)(node, _block_js__WEBPACK_IMPORTED_MODULE_5__.blocks)\n}\n\n/**\n * @param {Parents} node\n *   Node.\n * @returns {boolean}\n *   Whether `node` is skippable.\n */\nfunction skippable(node) {\n  return (\n    Boolean(node.type === 'element' && node.properties.hidden) ||\n    ignorableNode(node) ||\n    (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_3__.isElement)(node, _skippable_js__WEBPACK_IMPORTED_MODULE_6__.skippable)\n  )\n}\n\n/**\n * @param {string} character\n *   Character.\n * @returns {boolean}\n *   Whether `character` is removable.\n */\nfunction removable(character) {\n  return character === ' ' || character === '\\n'\n}\n\n/**\n * @type {Collapse}\n */\nfunction replaceNewlines(value) {\n  const match = /\\r?\\n|\\r/.exec(value)\n  return match ? match[0] : ' '\n}\n\n/**\n * @type {Collapse}\n */\nfunction replaceWhitespace() {\n  return ' '\n}\n\n/**\n * @param {Collapse} replace\n * @returns {Collapse}\n *   Collapse.\n */\nfunction collapseFactory(replace) {\n  return collapse\n\n  /**\n   * @type {Collapse}\n   */\n  function collapse(value) {\n    return String(value).replace(/[\\t\\n\\v\\f\\r ]+/g, replace)\n  }\n}\n\n/**\n * We don’t need to support void elements here (so `nobr wbr` -> `normal` is\n * ignored).\n *\n * @param {Parents} node\n *   Node.\n * @param {State} state\n *   Info passed around.\n * @returns {Whitespace}\n *   Whitespace.\n */\nfunction inferWhiteSpace(node, state) {\n  if ('tagName' in node && node.properties) {\n    switch (node.tagName) {\n      // Whitespace in script/style, while not displayed by CSS as significant,\n      // could have some meaning in JS/CSS, so we can’t touch them.\n      case 'listing':\n      case 'plaintext':\n      case 'script':\n      case 'style':\n      case 'xmp': {\n        return 'pre'\n      }\n\n      case 'nobr': {\n        return 'nowrap'\n      }\n\n      case 'pre': {\n        return node.properties.wrap ? 'pre-wrap' : 'pre'\n      }\n\n      case 'td':\n      case 'th': {\n        return node.properties.noWrap ? 'nowrap' : state.whitespace\n      }\n\n      case 'textarea': {\n        return 'pre-wrap'\n      }\n\n      default:\n    }\n  }\n\n  return state.whitespace\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLW1pbmlmeS13aGl0ZXNwYWNlL2xpYi9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUFBO0FBQ0EsWUFBWSxzQkFBc0I7QUFDbEM7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsV0FBVyxRQUFRO0FBQ25CO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsY0FBYyw0QkFBNEI7QUFDMUM7QUFDQSx5QkFBeUI7QUFDekI7QUFDQTtBQUNBO0FBQ0EsY0FBYyxTQUFTO0FBQ3ZCO0FBQ0EsY0FBYyxTQUFTO0FBQ3ZCO0FBQ0EsY0FBYyxTQUFTO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsY0FBYyxVQUFVO0FBQ3hCO0FBQ0EsY0FBYyxZQUFZO0FBQzFCO0FBQ0EsY0FBYyxxQkFBcUI7QUFDbkM7QUFDQSxjQUFjLHFCQUFxQjtBQUNuQztBQUNBO0FBQ0EsYUFBYSwwQ0FBMEM7QUFDdkQ7QUFDQTs7QUFFMkM7QUFDRztBQUNDO0FBQ1Y7QUFDSjtBQUNlO0FBQ007O0FBRXRELFdBQVcsU0FBUztBQUNwQjtBQUNBLHNCQUFzQixzREFBTzs7QUFFN0I7QUFDQTtBQUNBO0FBQ0EsV0FBVyxPQUFPO0FBQ2xCO0FBQ0EsV0FBVyw0QkFBNEI7QUFDdkM7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNPO0FBQ1A7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDs7QUFFQTtBQUNBLFdBQVcsT0FBTztBQUNsQjtBQUNBLFdBQVcsT0FBTztBQUNsQjtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNCQUFzQjs7QUFFdEI7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUEsVUFBVTtBQUNWOztBQUVBO0FBQ0EsV0FBVyxNQUFNO0FBQ2pCO0FBQ0EsV0FBVyxPQUFPO0FBQ2xCO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0JBQWtCO0FBQ2xCO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQSxXQUFXLFNBQVM7QUFDcEI7QUFDQSxXQUFXLE9BQU87QUFDbEI7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSzs7QUFFTDtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSxVQUFVO0FBQ1Y7O0FBRUE7QUFDQSxXQUFXLGNBQWM7QUFDekI7QUFDQSxXQUFXLFFBQVE7QUFDbkI7QUFDQSxXQUFXLHFCQUFxQjtBQUNoQztBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyxPQUFPO0FBQ2xCO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0EsSUFBSTtBQUNKLFNBQVMsZ0VBQVU7QUFDbkI7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsV0FBVyxPQUFPO0FBQ2xCO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBLFNBQVMsNERBQVEsVUFBVSwrREFBUyxPQUFPLGdEQUFRO0FBQ25EOztBQUVBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsT0FBTztBQUNsQjtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQSxTQUFTLCtEQUFTLE9BQU8sNkNBQU07QUFDL0I7O0FBRUE7QUFDQSxXQUFXLFNBQVM7QUFDcEI7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSSwrREFBUyxPQUFPLG9EQUFVO0FBQzlCO0FBQ0E7O0FBRUE7QUFDQSxXQUFXLFFBQVE7QUFDbkI7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBLFVBQVU7QUFDVjtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EsVUFBVTtBQUNWO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EsV0FBVyxVQUFVO0FBQ3JCLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBLFlBQVk7QUFDWjtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyxTQUFTO0FBQ3BCO0FBQ0EsV0FBVyxPQUFPO0FBQ2xCO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYmVuamlcXERlc2t0b3BcXEdfUFJPR1xcTmljb2xhc1xcYnRkYXNoLWVjb3N5c3RlbVxcYnRkYXNoc1xcbm9kZV9tb2R1bGVzXFxoYXN0LXV0aWwtbWluaWZ5LXdoaXRlc3BhY2VcXGxpYlxcaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAaW1wb3J0IHtOb2RlcywgUGFyZW50cywgVGV4dH0gZnJvbSAnaGFzdCdcbiAqL1xuXG4vKipcbiAqIEBjYWxsYmFjayBDb2xsYXBzZVxuICogICBDb2xsYXBzZSBhIHN0cmluZy5cbiAqIEBwYXJhbSB7c3RyaW5nfSB2YWx1ZVxuICogICBWYWx1ZSB0byBjb2xsYXBzZS5cbiAqIEByZXR1cm5zIHtzdHJpbmd9XG4gKiAgIENvbGxhcHNlZCB2YWx1ZS5cbiAqXG4gKiBAdHlwZWRlZiBPcHRpb25zXG4gKiAgIENvbmZpZ3VyYXRpb24uXG4gKiBAcHJvcGVydHkge2Jvb2xlYW4gfCBudWxsIHwgdW5kZWZpbmVkfSBbbmV3bGluZXM9ZmFsc2VdXG4gKiAgIENvbGxhcHNlIHdoaXRlc3BhY2UgY29udGFpbmluZyBuZXdsaW5lcyB0byBgJ1xcbidgIGluc3RlYWQgb2YgYCcgJ2BcbiAqICAgKGRlZmF1bHQ6IGBmYWxzZWApOyB0aGUgZGVmYXVsdCBpcyB0byBjb2xsYXBzZSB0byBhIHNpbmdsZSBzcGFjZS5cbiAqXG4gKiBAdHlwZWRlZiBSZXN1bHRcbiAqICAgUmVzdWx0LlxuICogQHByb3BlcnR5IHtib29sZWFufSByZW1vdmVcbiAqICAgV2hldGhlciB0byByZW1vdmUuXG4gKiBAcHJvcGVydHkge2Jvb2xlYW59IGlnbm9yZVxuICogICBXaGV0aGVyIHRvIGlnbm9yZS5cbiAqIEBwcm9wZXJ0eSB7Ym9vbGVhbn0gc3RyaXBBdFN0YXJ0XG4gKiAgIFdoZXRoZXIgdG8gc3RyaXAgYXQgdGhlIHN0YXJ0LlxuICpcbiAqIEB0eXBlZGVmIFN0YXRlXG4gKiAgIEluZm8gcGFzc2VkIGFyb3VuZC5cbiAqIEBwcm9wZXJ0eSB7Q29sbGFwc2V9IGNvbGxhcHNlXG4gKiAgIENvbGxhcHNlLlxuICogQHByb3BlcnR5IHtXaGl0ZXNwYWNlfSB3aGl0ZXNwYWNlXG4gKiAgIEN1cnJlbnQgd2hpdGVzcGFjZS5cbiAqIEBwcm9wZXJ0eSB7Ym9vbGVhbiB8IHVuZGVmaW5lZH0gW2JlZm9yZV1cbiAqICAgV2hldGhlciB0aGVyZSBpcyBhIGJyZWFrIGJlZm9yZSAoZGVmYXVsdDogYGZhbHNlYCkuXG4gKiBAcHJvcGVydHkge2Jvb2xlYW4gfCB1bmRlZmluZWR9IFthZnRlcl1cbiAqICAgV2hldGhlciB0aGVyZSBpcyBhIGJyZWFrIGFmdGVyIChkZWZhdWx0OiBgZmFsc2VgKS5cbiAqXG4gKiBAdHlwZWRlZiB7J25vcm1hbCcgfCAnbm93cmFwJyB8ICdwcmUnIHwgJ3ByZS13cmFwJ30gV2hpdGVzcGFjZVxuICogICBXaGl0ZXNwYWNlIHNldHRpbmcuXG4gKi9cblxuaW1wb3J0IHtlbWJlZGRlZH0gZnJvbSAnaGFzdC11dGlsLWVtYmVkZGVkJ1xuaW1wb3J0IHtpc0VsZW1lbnR9IGZyb20gJ2hhc3QtdXRpbC1pcy1lbGVtZW50J1xuaW1wb3J0IHt3aGl0ZXNwYWNlfSBmcm9tICdoYXN0LXV0aWwtd2hpdGVzcGFjZSdcbmltcG9ydCB7Y29udmVydH0gZnJvbSAndW5pc3QtdXRpbC1pcydcbmltcG9ydCB7YmxvY2tzfSBmcm9tICcuL2Jsb2NrLmpzJ1xuaW1wb3J0IHtjb250ZW50IGFzIGNvbnRlbnRzfSBmcm9tICcuL2NvbnRlbnQuanMnXG5pbXBvcnQge3NraXBwYWJsZSBhcyBza2lwcGFibGVzfSBmcm9tICcuL3NraXBwYWJsZS5qcydcblxuLyoqIEB0eXBlIHtPcHRpb25zfSAqL1xuY29uc3QgZW1wdHlPcHRpb25zID0ge31cbmNvbnN0IGlnbm9yYWJsZU5vZGUgPSBjb252ZXJ0KFsnY29tbWVudCcsICdkb2N0eXBlJ10pXG5cbi8qKlxuICogTWluaWZ5IHdoaXRlc3BhY2UuXG4gKlxuICogQHBhcmFtIHtOb2Rlc30gdHJlZVxuICogICBUcmVlLlxuICogQHBhcmFtIHtPcHRpb25zIHwgbnVsbCB8IHVuZGVmaW5lZH0gW29wdGlvbnNdXG4gKiAgIENvbmZpZ3VyYXRpb24gKG9wdGlvbmFsKS5cbiAqIEByZXR1cm5zIHt1bmRlZmluZWR9XG4gKiAgIE5vdGhpbmcuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBtaW5pZnlXaGl0ZXNwYWNlKHRyZWUsIG9wdGlvbnMpIHtcbiAgY29uc3Qgc2V0dGluZ3MgPSBvcHRpb25zIHx8IGVtcHR5T3B0aW9uc1xuXG4gIG1pbmlmeSh0cmVlLCB7XG4gICAgY29sbGFwc2U6IGNvbGxhcHNlRmFjdG9yeShcbiAgICAgIHNldHRpbmdzLm5ld2xpbmVzID8gcmVwbGFjZU5ld2xpbmVzIDogcmVwbGFjZVdoaXRlc3BhY2VcbiAgICApLFxuICAgIHdoaXRlc3BhY2U6ICdub3JtYWwnXG4gIH0pXG59XG5cbi8qKlxuICogQHBhcmFtIHtOb2Rlc30gbm9kZVxuICogICBOb2RlLlxuICogQHBhcmFtIHtTdGF0ZX0gc3RhdGVcbiAqICAgSW5mbyBwYXNzZWQgYXJvdW5kLlxuICogQHJldHVybnMge1Jlc3VsdH1cbiAqICAgUmVzdWx0LlxuICovXG5mdW5jdGlvbiBtaW5pZnkobm9kZSwgc3RhdGUpIHtcbiAgaWYgKCdjaGlsZHJlbicgaW4gbm9kZSkge1xuICAgIGNvbnN0IHNldHRpbmdzID0gey4uLnN0YXRlfVxuXG4gICAgaWYgKG5vZGUudHlwZSA9PT0gJ3Jvb3QnIHx8IGJsb2NrbGlrZShub2RlKSkge1xuICAgICAgc2V0dGluZ3MuYmVmb3JlID0gdHJ1ZVxuICAgICAgc2V0dGluZ3MuYWZ0ZXIgPSB0cnVlXG4gICAgfVxuXG4gICAgc2V0dGluZ3Mud2hpdGVzcGFjZSA9IGluZmVyV2hpdGVTcGFjZShub2RlLCBzdGF0ZSlcblxuICAgIHJldHVybiBhbGwobm9kZSwgc2V0dGluZ3MpXG4gIH1cblxuICBpZiAobm9kZS50eXBlID09PSAndGV4dCcpIHtcbiAgICBpZiAoc3RhdGUud2hpdGVzcGFjZSA9PT0gJ25vcm1hbCcpIHtcbiAgICAgIHJldHVybiBtaW5pZnlUZXh0KG5vZGUsIHN0YXRlKVxuICAgIH1cblxuICAgIC8vIE5hw692ZSBjb2xsYXBzZSwgYnV0IG5vIHRyaW1taW5nOlxuICAgIGlmIChzdGF0ZS53aGl0ZXNwYWNlID09PSAnbm93cmFwJykge1xuICAgICAgbm9kZS52YWx1ZSA9IHN0YXRlLmNvbGxhcHNlKG5vZGUudmFsdWUpXG4gICAgfVxuXG4gICAgLy8gVGhlIGBwcmUtd3JhcGAgb3IgYHByZWAgd2hpdGVzcGFjZSBzZXR0aW5ncyBhcmUgbmVpdGhlciBjb2xsYXBzZWQgbm9yXG4gICAgLy8gdHJpbW1lZC5cbiAgfVxuXG4gIHJldHVybiB7aWdub3JlOiBpZ25vcmFibGVOb2RlKG5vZGUpLCBzdHJpcEF0U3RhcnQ6IGZhbHNlLCByZW1vdmU6IGZhbHNlfVxufVxuXG4vKipcbiAqIEBwYXJhbSB7VGV4dH0gbm9kZVxuICogICBOb2RlLlxuICogQHBhcmFtIHtTdGF0ZX0gc3RhdGVcbiAqICAgSW5mbyBwYXNzZWQgYXJvdW5kLlxuICogQHJldHVybnMge1Jlc3VsdH1cbiAqICAgUmVzdWx0LlxuICovXG5mdW5jdGlvbiBtaW5pZnlUZXh0KG5vZGUsIHN0YXRlKSB7XG4gIGNvbnN0IHZhbHVlID0gc3RhdGUuY29sbGFwc2Uobm9kZS52YWx1ZSlcbiAgY29uc3QgcmVzdWx0ID0ge2lnbm9yZTogZmFsc2UsIHN0cmlwQXRTdGFydDogZmFsc2UsIHJlbW92ZTogZmFsc2V9XG4gIGxldCBzdGFydCA9IDBcbiAgbGV0IGVuZCA9IHZhbHVlLmxlbmd0aFxuXG4gIGlmIChzdGF0ZS5iZWZvcmUgJiYgcmVtb3ZhYmxlKHZhbHVlLmNoYXJBdCgwKSkpIHtcbiAgICBzdGFydCsrXG4gIH1cblxuICBpZiAoc3RhcnQgIT09IGVuZCAmJiByZW1vdmFibGUodmFsdWUuY2hhckF0KGVuZCAtIDEpKSkge1xuICAgIGlmIChzdGF0ZS5hZnRlcikge1xuICAgICAgZW5kLS1cbiAgICB9IGVsc2Uge1xuICAgICAgcmVzdWx0LnN0cmlwQXRTdGFydCA9IHRydWVcbiAgICB9XG4gIH1cblxuICBpZiAoc3RhcnQgPT09IGVuZCkge1xuICAgIHJlc3VsdC5yZW1vdmUgPSB0cnVlXG4gIH0gZWxzZSB7XG4gICAgbm9kZS52YWx1ZSA9IHZhbHVlLnNsaWNlKHN0YXJ0LCBlbmQpXG4gIH1cblxuICByZXR1cm4gcmVzdWx0XG59XG5cbi8qKlxuICogQHBhcmFtIHtQYXJlbnRzfSBwYXJlbnRcbiAqICAgTm9kZS5cbiAqIEBwYXJhbSB7U3RhdGV9IHN0YXRlXG4gKiAgIEluZm8gcGFzc2VkIGFyb3VuZC5cbiAqIEByZXR1cm5zIHtSZXN1bHR9XG4gKiAgIFJlc3VsdC5cbiAqL1xuZnVuY3Rpb24gYWxsKHBhcmVudCwgc3RhdGUpIHtcbiAgbGV0IGJlZm9yZSA9IHN0YXRlLmJlZm9yZVxuICBjb25zdCBhZnRlciA9IHN0YXRlLmFmdGVyXG4gIGNvbnN0IGNoaWxkcmVuID0gcGFyZW50LmNoaWxkcmVuXG4gIGxldCBsZW5ndGggPSBjaGlsZHJlbi5sZW5ndGhcbiAgbGV0IGluZGV4ID0gLTFcblxuICB3aGlsZSAoKytpbmRleCA8IGxlbmd0aCkge1xuICAgIGNvbnN0IHJlc3VsdCA9IG1pbmlmeShjaGlsZHJlbltpbmRleF0sIHtcbiAgICAgIC4uLnN0YXRlLFxuICAgICAgYWZ0ZXI6IGNvbGxhcHNhYmxlQWZ0ZXIoY2hpbGRyZW4sIGluZGV4LCBhZnRlciksXG4gICAgICBiZWZvcmVcbiAgICB9KVxuXG4gICAgaWYgKHJlc3VsdC5yZW1vdmUpIHtcbiAgICAgIGNoaWxkcmVuLnNwbGljZShpbmRleCwgMSlcbiAgICAgIGluZGV4LS1cbiAgICAgIGxlbmd0aC0tXG4gICAgfSBlbHNlIGlmICghcmVzdWx0Lmlnbm9yZSkge1xuICAgICAgYmVmb3JlID0gcmVzdWx0LnN0cmlwQXRTdGFydFxuICAgIH1cblxuICAgIC8vIElmIHRoaXMgZWxlbWVudCwgc3VjaCBhcyBhIGA8c2VsZWN0PmAgb3IgYDxpbWc+YCwgY29udHJpYnV0ZXMgY29udGVudFxuICAgIC8vIHNvbWVob3csIGFsbG93IHdoaXRlc3BhY2UgYWdhaW4uXG4gICAgaWYgKGNvbnRlbnQoY2hpbGRyZW5baW5kZXhdKSkge1xuICAgICAgYmVmb3JlID0gZmFsc2VcbiAgICB9XG4gIH1cblxuICByZXR1cm4ge2lnbm9yZTogZmFsc2UsIHN0cmlwQXRTdGFydDogQm9vbGVhbihiZWZvcmUgfHwgYWZ0ZXIpLCByZW1vdmU6IGZhbHNlfVxufVxuXG4vKipcbiAqIEBwYXJhbSB7QXJyYXk8Tm9kZXM+fSBub2Rlc1xuICogICBOb2Rlcy5cbiAqIEBwYXJhbSB7bnVtYmVyfSBpbmRleFxuICogICBJbmRleC5cbiAqIEBwYXJhbSB7Ym9vbGVhbiB8IHVuZGVmaW5lZH0gW2FmdGVyXVxuICogICBXaGV0aGVyIHRoZXJlIGlzIGEgYnJlYWsgYWZ0ZXIgYG5vZGVzYCAoZGVmYXVsdDogYGZhbHNlYCkuXG4gKiBAcmV0dXJucyB7Ym9vbGVhbiB8IHVuZGVmaW5lZH1cbiAqICAgV2hldGhlciB0aGVyZSBpcyBhIGJyZWFrIGFmdGVyIHRoZSBub2RlIGF0IGBpbmRleGAuXG4gKi9cbmZ1bmN0aW9uIGNvbGxhcHNhYmxlQWZ0ZXIobm9kZXMsIGluZGV4LCBhZnRlcikge1xuICB3aGlsZSAoKytpbmRleCA8IG5vZGVzLmxlbmd0aCkge1xuICAgIGNvbnN0IG5vZGUgPSBub2Rlc1tpbmRleF1cbiAgICBsZXQgcmVzdWx0ID0gaW5mZXJCb3VuZGFyeShub2RlKVxuXG4gICAgaWYgKHJlc3VsdCA9PT0gdW5kZWZpbmVkICYmICdjaGlsZHJlbicgaW4gbm9kZSAmJiAhc2tpcHBhYmxlKG5vZGUpKSB7XG4gICAgICByZXN1bHQgPSBjb2xsYXBzYWJsZUFmdGVyKG5vZGUuY2hpbGRyZW4sIC0xKVxuICAgIH1cblxuICAgIGlmICh0eXBlb2YgcmVzdWx0ID09PSAnYm9vbGVhbicpIHtcbiAgICAgIHJldHVybiByZXN1bHRcbiAgICB9XG4gIH1cblxuICByZXR1cm4gYWZ0ZXJcbn1cblxuLyoqXG4gKiBJbmZlciB0d28gdHlwZXMgb2YgYm91bmRhcmllczpcbiAqXG4gKiAxLiBgdHJ1ZWAg4oCUIGJvdW5kYXJ5IGZvciB3aGljaCB3aGl0ZXNwYWNlIGFyb3VuZCBpdCBkb2VzIG5vdCBjb250cmlidXRlXG4gKiAgICBhbnl0aGluZ1xuICogMi4gYGZhbHNlYCDigJQgYm91bmRhcnkgZm9yIHdoaWNoIHdoaXRlc3BhY2UgYXJvdW5kIGl0ICpkb2VzKiBjb250cmlidXRlXG4gKlxuICogTm8gcmVzdWx0IChgdW5kZWZpbmVkYCkgaXMgcmV0dXJuZWQgaWYgaXQgaXMgdW5rbm93bi5cbiAqXG4gKiBAcGFyYW0ge05vZGVzfSBub2RlXG4gKiAgIE5vZGUuXG4gKiBAcmV0dXJucyB7Ym9vbGVhbiB8IHVuZGVmaW5lZH1cbiAqICAgQm91bmRhcnkuXG4gKi9cbmZ1bmN0aW9uIGluZmVyQm91bmRhcnkobm9kZSkge1xuICBpZiAobm9kZS50eXBlID09PSAnZWxlbWVudCcpIHtcbiAgICBpZiAoY29udGVudChub2RlKSkge1xuICAgICAgcmV0dXJuIGZhbHNlXG4gICAgfVxuXG4gICAgaWYgKGJsb2NrbGlrZShub2RlKSkge1xuICAgICAgcmV0dXJuIHRydWVcbiAgICB9XG5cbiAgICAvLyBVbmtub3duOiBlaXRoZXIgZGVwZW5kcyBvbiBzaWJsaW5ncyBpZiBlbWJlZGRlZCBvciBtZXRhZGF0YSwgb3Igb25cbiAgICAvLyBjaGlsZHJlbi5cbiAgfSBlbHNlIGlmIChub2RlLnR5cGUgPT09ICd0ZXh0Jykge1xuICAgIGlmICghd2hpdGVzcGFjZShub2RlKSkge1xuICAgICAgcmV0dXJuIGZhbHNlXG4gICAgfVxuICB9IGVsc2UgaWYgKCFpZ25vcmFibGVOb2RlKG5vZGUpKSB7XG4gICAgcmV0dXJuIGZhbHNlXG4gIH1cbn1cblxuLyoqXG4gKiBJbmZlciB3aGV0aGVyIGEgbm9kZSBpcyBza2lwcGFibGUuXG4gKlxuICogQHBhcmFtIHtOb2Rlc30gbm9kZVxuICogICBOb2RlLlxuICogQHJldHVybnMge2Jvb2xlYW59XG4gKiAgIFdoZXRoZXIgYG5vZGVgIGlzIHNraXBwYWJsZS5cbiAqL1xuZnVuY3Rpb24gY29udGVudChub2RlKSB7XG4gIHJldHVybiBlbWJlZGRlZChub2RlKSB8fCBpc0VsZW1lbnQobm9kZSwgY29udGVudHMpXG59XG5cbi8qKlxuICogU2VlOiA8aHR0cHM6Ly9odG1sLnNwZWMud2hhdHdnLm9yZy8jdGhlLWNzcy11c2VyLWFnZW50LXN0eWxlLXNoZWV0LWFuZC1wcmVzZW50YXRpb25hbC1oaW50cz5cbiAqXG4gKiBAcGFyYW0ge05vZGVzfSBub2RlXG4gKiAgIE5vZGUuXG4gKiBAcmV0dXJucyB7Ym9vbGVhbn1cbiAqICAgV2hldGhlciBgbm9kZWAgaXMgYmxvY2stbGlrZS5cbiAqL1xuZnVuY3Rpb24gYmxvY2tsaWtlKG5vZGUpIHtcbiAgcmV0dXJuIGlzRWxlbWVudChub2RlLCBibG9ja3MpXG59XG5cbi8qKlxuICogQHBhcmFtIHtQYXJlbnRzfSBub2RlXG4gKiAgIE5vZGUuXG4gKiBAcmV0dXJucyB7Ym9vbGVhbn1cbiAqICAgV2hldGhlciBgbm9kZWAgaXMgc2tpcHBhYmxlLlxuICovXG5mdW5jdGlvbiBza2lwcGFibGUobm9kZSkge1xuICByZXR1cm4gKFxuICAgIEJvb2xlYW4obm9kZS50eXBlID09PSAnZWxlbWVudCcgJiYgbm9kZS5wcm9wZXJ0aWVzLmhpZGRlbikgfHxcbiAgICBpZ25vcmFibGVOb2RlKG5vZGUpIHx8XG4gICAgaXNFbGVtZW50KG5vZGUsIHNraXBwYWJsZXMpXG4gIClcbn1cblxuLyoqXG4gKiBAcGFyYW0ge3N0cmluZ30gY2hhcmFjdGVyXG4gKiAgIENoYXJhY3Rlci5cbiAqIEByZXR1cm5zIHtib29sZWFufVxuICogICBXaGV0aGVyIGBjaGFyYWN0ZXJgIGlzIHJlbW92YWJsZS5cbiAqL1xuZnVuY3Rpb24gcmVtb3ZhYmxlKGNoYXJhY3Rlcikge1xuICByZXR1cm4gY2hhcmFjdGVyID09PSAnICcgfHwgY2hhcmFjdGVyID09PSAnXFxuJ1xufVxuXG4vKipcbiAqIEB0eXBlIHtDb2xsYXBzZX1cbiAqL1xuZnVuY3Rpb24gcmVwbGFjZU5ld2xpbmVzKHZhbHVlKSB7XG4gIGNvbnN0IG1hdGNoID0gL1xccj9cXG58XFxyLy5leGVjKHZhbHVlKVxuICByZXR1cm4gbWF0Y2ggPyBtYXRjaFswXSA6ICcgJ1xufVxuXG4vKipcbiAqIEB0eXBlIHtDb2xsYXBzZX1cbiAqL1xuZnVuY3Rpb24gcmVwbGFjZVdoaXRlc3BhY2UoKSB7XG4gIHJldHVybiAnICdcbn1cblxuLyoqXG4gKiBAcGFyYW0ge0NvbGxhcHNlfSByZXBsYWNlXG4gKiBAcmV0dXJucyB7Q29sbGFwc2V9XG4gKiAgIENvbGxhcHNlLlxuICovXG5mdW5jdGlvbiBjb2xsYXBzZUZhY3RvcnkocmVwbGFjZSkge1xuICByZXR1cm4gY29sbGFwc2VcblxuICAvKipcbiAgICogQHR5cGUge0NvbGxhcHNlfVxuICAgKi9cbiAgZnVuY3Rpb24gY29sbGFwc2UodmFsdWUpIHtcbiAgICByZXR1cm4gU3RyaW5nKHZhbHVlKS5yZXBsYWNlKC9bXFx0XFxuXFx2XFxmXFxyIF0rL2csIHJlcGxhY2UpXG4gIH1cbn1cblxuLyoqXG4gKiBXZSBkb27igJl0IG5lZWQgdG8gc3VwcG9ydCB2b2lkIGVsZW1lbnRzIGhlcmUgKHNvIGBub2JyIHdicmAgLT4gYG5vcm1hbGAgaXNcbiAqIGlnbm9yZWQpLlxuICpcbiAqIEBwYXJhbSB7UGFyZW50c30gbm9kZVxuICogICBOb2RlLlxuICogQHBhcmFtIHtTdGF0ZX0gc3RhdGVcbiAqICAgSW5mbyBwYXNzZWQgYXJvdW5kLlxuICogQHJldHVybnMge1doaXRlc3BhY2V9XG4gKiAgIFdoaXRlc3BhY2UuXG4gKi9cbmZ1bmN0aW9uIGluZmVyV2hpdGVTcGFjZShub2RlLCBzdGF0ZSkge1xuICBpZiAoJ3RhZ05hbWUnIGluIG5vZGUgJiYgbm9kZS5wcm9wZXJ0aWVzKSB7XG4gICAgc3dpdGNoIChub2RlLnRhZ05hbWUpIHtcbiAgICAgIC8vIFdoaXRlc3BhY2UgaW4gc2NyaXB0L3N0eWxlLCB3aGlsZSBub3QgZGlzcGxheWVkIGJ5IENTUyBhcyBzaWduaWZpY2FudCxcbiAgICAgIC8vIGNvdWxkIGhhdmUgc29tZSBtZWFuaW5nIGluIEpTL0NTUywgc28gd2UgY2Fu4oCZdCB0b3VjaCB0aGVtLlxuICAgICAgY2FzZSAnbGlzdGluZyc6XG4gICAgICBjYXNlICdwbGFpbnRleHQnOlxuICAgICAgY2FzZSAnc2NyaXB0JzpcbiAgICAgIGNhc2UgJ3N0eWxlJzpcbiAgICAgIGNhc2UgJ3htcCc6IHtcbiAgICAgICAgcmV0dXJuICdwcmUnXG4gICAgICB9XG5cbiAgICAgIGNhc2UgJ25vYnInOiB7XG4gICAgICAgIHJldHVybiAnbm93cmFwJ1xuICAgICAgfVxuXG4gICAgICBjYXNlICdwcmUnOiB7XG4gICAgICAgIHJldHVybiBub2RlLnByb3BlcnRpZXMud3JhcCA/ICdwcmUtd3JhcCcgOiAncHJlJ1xuICAgICAgfVxuXG4gICAgICBjYXNlICd0ZCc6XG4gICAgICBjYXNlICd0aCc6IHtcbiAgICAgICAgcmV0dXJuIG5vZGUucHJvcGVydGllcy5ub1dyYXAgPyAnbm93cmFwJyA6IHN0YXRlLndoaXRlc3BhY2VcbiAgICAgIH1cblxuICAgICAgY2FzZSAndGV4dGFyZWEnOiB7XG4gICAgICAgIHJldHVybiAncHJlLXdyYXAnXG4gICAgICB9XG5cbiAgICAgIGRlZmF1bHQ6XG4gICAgfVxuICB9XG5cbiAgcmV0dXJuIHN0YXRlLndoaXRlc3BhY2Vcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-minify-whitespace/lib/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-minify-whitespace/lib/skippable.js":
/*!*******************************************************************!*\
  !*** ./node_modules/hast-util-minify-whitespace/lib/skippable.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   skippable: () => (/* binding */ skippable)\n/* harmony export */ });\nconst skippable = [\n  'area',\n  'base',\n  'basefont',\n  'dialog',\n  'datalist',\n  'head',\n  'link',\n  'meta',\n  'noembed',\n  'noframes',\n  'param',\n  'rp',\n  'script',\n  'source',\n  'style',\n  'template',\n  'track',\n  'title'\n]\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLW1pbmlmeS13aGl0ZXNwYWNlL2xpYi9za2lwcGFibGUuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYmVuamlcXERlc2t0b3BcXEdfUFJPR1xcTmljb2xhc1xcYnRkYXNoLWVjb3N5c3RlbVxcYnRkYXNoc1xcbm9kZV9tb2R1bGVzXFxoYXN0LXV0aWwtbWluaWZ5LXdoaXRlc3BhY2VcXGxpYlxcc2tpcHBhYmxlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCBza2lwcGFibGUgPSBbXG4gICdhcmVhJyxcbiAgJ2Jhc2UnLFxuICAnYmFzZWZvbnQnLFxuICAnZGlhbG9nJyxcbiAgJ2RhdGFsaXN0JyxcbiAgJ2hlYWQnLFxuICAnbGluaycsXG4gICdtZXRhJyxcbiAgJ25vZW1iZWQnLFxuICAnbm9mcmFtZXMnLFxuICAncGFyYW0nLFxuICAncnAnLFxuICAnc2NyaXB0JyxcbiAgJ3NvdXJjZScsXG4gICdzdHlsZScsXG4gICd0ZW1wbGF0ZScsXG4gICd0cmFjaycsXG4gICd0aXRsZSdcbl1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-minify-whitespace/lib/skippable.js\n");

/***/ })

};
;