"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/rehype-remark";
exports.ids = ["vendor-chunks/rehype-remark"];
exports.modules = {

/***/ "(ssr)/./node_modules/rehype-remark/lib/index.js":
/*!*************************************************!*\
  !*** ./node_modules/rehype-remark/lib/index.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ rehypeRemark)\n/* harmony export */ });\n/* harmony import */ var hast_util_to_mdast__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! hast-util-to-mdast */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/index.js\");\n/**\n * @import {Root as HastRoot} from 'hast'\n * @import {Options} from 'hast-util-to-mdast'\n * @import {Root as MdastRoot} from 'mdast'\n * @import {Processor} from 'unified'\n * @import {VFile} from 'vfile'\n */\n\n/**\n * @callback TransformBridge\n *   Bridge-mode.\n *\n *   Runs the destination with the new mdast tree.\n *   Discards result.\n * @param {HastRoot} tree\n *   Tree.\n * @param {VFile} file\n *   File.\n * @returns {Promise<undefined>}\n *   Nothing.\n *\n * @callback TransformMutate\n *  Mutate-mode.\n *\n *  Further transformers run on the mdast tree.\n * @param {HastRoot} tree\n *   Tree.\n * @param {VFile} file\n *   File.\n * @returns {MdastRoot}\n *   Tree (mdast).\n */\n\n\n\n/** @satisfies {Options} */\nconst defaults = {document: true}\n\n/**\n * Turn HTML into markdown.\n *\n * ###### Notes\n *\n * *   if a processor is given, runs the (remark) plugins used on it with an\n *     mdast tree, then discards the result (*bridge mode*)\n * *   otherwise, returns an mdast tree, the plugins used after `rehypeRemark`\n *     are remark plugins (*mutate mode*)\n *\n * > 👉 **Note**: It’s highly unlikely that you want to pass a `processor`.\n *\n * @overload\n * @param {Processor} processor\n * @param {Options | null | undefined} [options]\n * @returns {TransformBridge}\n *\n * @overload\n * @param {Options | null | undefined} [options]\n * @returns {TransformMutate}\n *\n * @overload\n * @param {Options | Processor | null | undefined} [destination]\n * @param {Options | null | undefined} [options]\n * @returns {TransformBridge | TransformMutate}\n *\n * @param {Options | Processor | null | undefined} [destination]\n *   Processor or configuration (optional).\n * @param {Options | null | undefined} [options]\n *   When a processor was given, configuration (optional).\n * @returns {TransformBridge | TransformMutate}\n *   Transform.\n */\nfunction rehypeRemark(destination, options) {\n  if (destination && 'run' in destination) {\n    /**\n     * @type {TransformBridge}\n     */\n    return async function (tree, file) {\n      const mdastTree = (0,hast_util_to_mdast__WEBPACK_IMPORTED_MODULE_0__.toMdast)(tree, {...defaults, ...options})\n      await destination.run(mdastTree, file)\n    }\n  }\n\n  /**\n   * @type {TransformMutate}\n   */\n  return function (tree) {\n    return /** @type {MdastRoot} */ (\n      (0,hast_util_to_mdast__WEBPACK_IMPORTED_MODULE_0__.toMdast)(tree, {...defaults, ...destination})\n    )\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rehype-remark/lib/index.js\n");

/***/ })

};
;