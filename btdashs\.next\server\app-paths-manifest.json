{"/api/user/me/route": "app/api/user/me/route.js", "/api/user/educations/route": "app/api/user/educations/route.js", "/api/user/jobs/route": "app/api/user/jobs/route.js", "/api/user/events/route": "app/api/user/events/route.js", "/api/user/skills/route": "app/api/user/skills/route.js", "/api/user/experiences/route": "app/api/user/experiences/route.js", "/api/user/preferences/route": "app/api/user/preferences/route.js", "/page": "app/page.js", "/profile/page": "app/profile/page.js", "/subnets/page": "app/subnets/page.js"}